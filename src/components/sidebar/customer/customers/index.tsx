import { ColDef, ICellRendererParams } from 'ag-grid-community';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Divider, But<PERSON> } from 'antd';
import Icon from '@ant-design/icons/lib/components/Icon';
import { ROUTES } from '@/constant/RoutesConstant';
import {
  AssignToOutlined,
  DeleteIcon,
  deleteSvg,
  DuplicateCustomerIcon,
  EyeIcon,
  HistoryIcon,
  PlusButtonIcon,
  PrinterIcon,
} from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import GridTooltip from '@/components/common/gridTooltip/GridTooltip';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { IContextMenuItems } from '@customTypes/ContextMenuTypes';
import { customAlert } from '../../../common/customAlert/CustomAlert';
import ColumnManage from '@components/specific/columnManage';
import { IColDef } from '@/types/AgGridTypes';
import { GridNames } from '@/types/AppEvents';
import { useLanguage } from '@/hooks/useLanguage';

interface ICustomer {
  companyName: string;
  accountNumber: string;
  contactName: string;
  addressLine1: string;
  city: string;
  phone: string;
  email: string;
  fax: string;
  status: boolean;
  category: string;
  dateUpdated: string;
  lastUpdateBy: string;
}

const CustomersComponent = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const handleModalClosing = () => {
    customAlert.destroy();
  };
  const customerColDefs: IColDef[] = [
    {
      field: 'companyName',
      headerName: `${t('dashboard.customer.columns.companyName')}`,
      unSortIcon: true,
      tooltipField: 'companyName',
      type: 'string',
      visible: true,
    },
    {
      field: 'accountNumber',
      headerName: `${t('dashboard.customer.columns.accountNumber')}`,
      unSortIcon: true,
      width: 165,
      visible: true,
    },
    {
      field: 'contactName',
      headerName: `${t('dashboard.customer.columns.contactName')}`,
      unSortIcon: true,
      width: 149,
      visible: true,
    },
    {
      field: 'addressLine1',
      headerName: `${t('dashboard.customer.columns.addressLine1')}`,
      unSortIcon: true,
      width: 136,
      visible: true,
    },
    {
      field: 'city',
      headerName: `${t('dashboard.customer.columns.city')}`,
      unSortIcon: true,
      width: 109,
      visible: true,
    },
    {
      field: 'phone',
      headerName: `${t('dashboard.customer.columns.phone')}`,
      unSortIcon: true,
      width: 132,
      visible: true,
    },
    {
      field: 'email',
      headerName: `${t('dashboard.customer.columns.email')}`,
      unSortIcon: true,
      width: 132,
      visible: true,
    },
    {
      field: 'fax',
      headerName: `${t('dashboard.customer.columns.fax')}`,
      unSortIcon: true,
      width: 132,
      visible: true,
    },
    {
      field: 'status',
      headerName: `${t('dashboard.customer.columns.status')}`,
      unSortIcon: true,
      width: 132,
      cellRenderer: (params: ICellRendererParams) => {
        return params.data.status ? (
          <>
            <span className="h-[10px] w-[10px] rounded-full bg-[seagreen] inline-block mr-1" />
            Active
          </>
        ) : (
          <>
            <span className="h-[10px] w-[10px] rounded-full bg-red-600 inline-block mr-1" />
            Inactive
          </>
        );
      },
      visible: true,
    },
    {
      field: 'category',
      headerName: `${t('dashboard.customer.columns.category')}`,
      unSortIcon: true,
      width: 132,
      visible: true,
    },
    {
      field: 'dateAdded',
      headerName: `${t('dashboard.customer.columns.dateAdded')}`,
      unSortIcon: true,
      width: 132,
      visible: true,
    },
    {
      field: 'dateUpdated',
      headerName: `${t('dashboard.customer.columns.dateUpdated')}`,
      unSortIcon: true,
      width: 132,
      visible: true,
      type: 'date',
    },
    {
      field: 'lastUpdateBy',
      headerName: `${t('dashboard.customer.columns.lastUpdateBy')}`,
      unSortIcon: true,
      width: 132,
      visible: true,
    },
    {
      field: 'action',
      sortable: false,
      resizable: false,
      headerName: `${t('dashboard.customer.columns.action')}`,
      pinned: 'right',
      width: 110,
      cellRenderer: () => {
        return (
          <div className="flex gap-2 h-full items-center w-full overflow-hidden">
            <CustomTooltip
              content={
                <div className="text-[#20363f] flex flex-col text-xs font-medium gap-1 w-full">
                  Added:
                  <span className=" block w-fit text-sm font-semibold">01/11/21 02:25PM</span>
                  <hr className="border-[#0000001c]" />
                  Modified:{' '}
                  <span className=" block w-fit text-sm font-semibold">01/11/21 02:25PM</span>
                  <hr className="border-[#0000001c]" />
                  Last updated By:{' '}
                  <span className="block w-fit text-sm font-semibold">Cameron Williamson</span>
                </div>
              }
              placement="leftTop"
            >
              <div>
                <Icon component={HistoryIcon} className="cursor-pointer mt-2" alt="history" />
              </div>
            </CustomTooltip>
            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              onClick={() => {
                customAlert.success({
                  firstButtonFunction: () => handleModalClosing(),
                  secondButtonFunction: () => handleModalClosing(),
                });
              }}
            />
            <Icon component={DeleteIcon} className="cursor-pointer" />
          </div>
        );
      },
      visible: true,
    },
  ];

  function generateRandomCustomerData(dataArray: ICustomer[], count: number) {
    const randomData = [];

    for (let i = 0; i < count; i++) {
      const randomIndex = Math.floor(Math.random() * dataArray.length);
      const selectedEntry = { ...dataArray[randomIndex] };

      selectedEntry.status = Math.random() < 0.5 ? true : false;

      randomData.push(selectedEntry);
    }

    return randomData;
  }
  const customerRowData = [
    {
      companyName: 'Life - Life Corporation',
      accountNumber: '********',
      contactName: 'Dianne Russell',
      addressLine1: '567 cedar street Mumbai',
      city: 'Austin',
      phone: '+ 49 **********',
      email: '<EMAIL>',
      fax: '+ 49 **********',
      status: true,
      category: 'Home & Office',
      dateAdded: '12/06/2020',
      dateUpdated: '12/06/2020',
      lastUpdateBy: 'Annette Black',
    },
    {
      companyName: 'Yates - Yates Entertainment',
      accountNumber: '********',
      contactName: 'Cody Fisher',
      addressLine1: '890 Birch Cordiform',
      city: 'Naperville',
      phone: '+ 49 **********',
      email: '<EMAIL>',
      fax: '+ 49 **********',
      status: true,
      category: 'Equipment Restaurants',
      dateAdded: '15/08/2017',
      dateUpdated: '12/06/2020',
      lastUpdateBy: 'Wade Warren',
    },
  ];
  const randomCustomer = useMemo(() => generateRandomCustomerData(customerRowData, 50), []);

  const customerContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: `${t('dashboard.newCustomer')}`,
        key: 'Open',
        icon: AssignToOutlined as React.ElementType,
        onClick: () => {
          alert('from click');
        },
      },
      {
        label: `${t('dashboard.duplicateCustomer')}`,
        icon: DuplicateCustomerIcon as React.ElementType,
        key: 'assignTo',
      },
      {
        label: `${t('common.print')}`,
        icon: PrinterIcon as React.ElementType,
        key: 'unAssign',
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} />) as unknown as React.ElementType,
        key: 'delete',
      },
    ];
  }, [t]);

  const defaultColDef = useMemo<ColDef>(() => {
    return {
      tooltipComponent: GridTooltip,
    };
  }, []);

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col overflow-hidden bg-white">
        <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
          <div className="md:w-1/3 flex flex-col 3xsm:w-full">
            <PageHeadingComponent title={'Customers'} />
          </div>
          <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
            <div className="flex gap-3">
              <ColumnManage colDefs={customerColDefs} gridName={GridNames.customerGrid} />
            </div>
            <div className="pt-5">
              <Divider type="vertical" className="h-[40px] !m-0" />
            </div>
            <div className="pt-5">
              <Button
                onClick={() => {
                  navigate(ROUTES.CUSTOMER.CUSTOMER_ADD);
                }}
                className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                icon={<PlusButtonIcon />}
              >
                {t('dashboard.addCustomer')}
              </Button>
            </div>
          </div>
        </div>{' '}
        <main className=" h-screen overflow-x-hidden overflow-y-auto bg-white">
          <div className=" mx-auto pr-6 py-5 h-full flex justify-center items-center">
            <CustomAgGrid
              gridName={GridNames.customerGrid}
              rowData={[...customerRowData, ...randomCustomer]}
              columnDefs={customerColDefs}
              isContextMenu
              contextMenuItem={customerContextMenuItems}
              onContextMenu={() => {}}
              defaultColDef={defaultColDef}
            />
          </div>
        </main>
      </div>
    </div>
  );
};

export default CustomersComponent;
