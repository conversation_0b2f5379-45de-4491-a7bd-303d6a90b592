import { Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import SearchFilterComponent from '../../../specific/searchFilter/SearchFilterComponent';
import { PlusButtonIcon } from '../../../../assets/icons/plusButtonIcon';
import PageHeadingComponent from '../../../specific/pageHeading/PageHeadingComponent';
import ColumnManageComponent from '../../../specific/columnManage/ColumnManageComponent';
import PermissionChecker from '../../../specific/permissionChecker/PermissionChecker';
import { ROUTES } from '@/constant/RoutesConstant';
import { useLanguage } from '@/hooks/useLanguage';

const CustomerHeader = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  return (
    <>
      <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
        <div className="md:w-1/3 flex flex-col 3xsm:w-full">
          <PageHeadingComponent title={'Customers'} />
        </div>

        <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
          <SearchFilterComponent searchedValues={() => {}} colDefs={[]} />
          <div className="pt-5">
            <PermissionChecker action="create" resource="customer" type="popover">
              <Button
                onClick={() => {
                  navigate(ROUTES.CUSTOMER.CUSTOMER_ADD);
                }}
                className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                icon={<PlusButtonIcon />}
              >
                {t('dashboard.addCustomer')}
              </Button>
            </PermissionChecker>
          </div>

          <ColumnManageComponent colDefs={[]} />
        </div>
      </div>
    </>
  );
};
export default CustomerHeader;
