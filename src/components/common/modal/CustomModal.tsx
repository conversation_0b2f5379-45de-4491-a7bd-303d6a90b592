import React from 'react';
import { Modal, ModalProps } from 'antd';
import './customModal.css';
import { closeNotifyIcon } from '@/assets';

interface ICustomModalProps extends ModalProps {
  modalTitle: string;
  modalDescription: string;
}

const CustomModal: React.FC<ICustomModalProps> = (props) => {
  const { modalTitle, modalDescription } = props;

  const modalStyle = {
    mask: {
      backdropFilter: 'blur(2px)',
    },
  };

  const ModalTitle = (
    <header className="custom-modals-header ">
      <h5 className="text-lg">{modalTitle}</h5>
      <p className="text-sm text-primary-200 font-normal w-[90%]">{modalDescription}</p>
    </header>
  );

  return (
    <Modal
      prefixCls="custom-modal"
      title={ModalTitle}
      styles={modalStyle}
      closeIcon={<img src={closeNotifyIcon} width={20} />}
      destroyOnClose
      {...props}
    />
  );
};

export default CustomModal;
