.menu-option {
  @apply flex h-[40px] justify-between items-center py-[10px] px-3.5 cursor-pointer relative rounded-md;
}

.option-label-wrapper {
  @apply flex items-center gap-[10px];
}

.submenu-wrapper {
  @apply opacity-0 invisible flex-col gap-2 py-3.5 absolute left-[100%];
  @apply top-0 max-h-[400px] max-w-[212px] w-full bg-[#FFFFFF];
  @apply cursor-pointer shadow-contextMenu rounded-lg;
  transition: 0.2s;
}

.context-menu-wrapper {
  @apply opacity-0 invisible w-fit flex-col flex gap-2 max-h-[400px] min-w-[212px] max-w-[300px];
  @apply text-sm absolute top-[50%] left-[50%] bg-[#FFFFFF];
  @apply z-[1000] shadow-contextMenu rounded-lg py-3.5 select-none;
  transition: 0.2s;
}
.submenu {
  transform: translateY(-10px); /* Adds subtle movement */
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.hide-context-menu {
  @apply invisible overflow-hidden;
}

.show-submenu {
  @apply !opacity-100 !visible;
  transform: translateY(0) !important;
}

.hide-submenu {
  @apply opacity-0 invisible overflow-hidden;
  transform: translateY(0);
}
