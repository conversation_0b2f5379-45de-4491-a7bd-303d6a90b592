import React, { memo, useCallback, useLayoutEffect, useRef, useState } from 'react';
import { subMenuSvg } from '@/assets';
import { IContextMenuItems, IGridContextMenuProps } from '@customTypes/ContextMenuTypes';
import './gridContextMenu.css';
import { CellContextMenuEvent } from 'ag-grid-community';

/**
 * A custom context menu component for an ag-Grid table.
 *
 * This component renders a context menu based on the provided `menuItems` and allows
 * for customized menu options such as submenus, icons.
 *
 * @component
 * @param {IGridContextMenuProps} props - includes context Menu items Array.
 */

const GridContextMenu: React.FC<IGridContextMenuProps> = (props) => {
  const { menuItems, contextMenuEvent } = props;
  const [hoveredItemTree, setHoveredItemTree] = useState<string[]>([]);

  const contextMenuRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    const onEscapeHandler = (event: KeyboardEvent) => {
      if (
        contextMenuRef.current &&
        contextMenuRef.current.style.opacity === '1' &&
        event.key === 'Escape'
      ) {
        closeContextMenu();
      }
    };
    document.body.addEventListener('keydown', onEscapeHandler);

    return () => {
      document.body.removeEventListener('keydown', onEscapeHandler);
    };
  }, []);

  /**
   * Persists the hierarchy of context menu and submenu options as the user navigates.
   *
   * Only update Menu Items Tree if hovered path has changed
   * @example
   * Trigger notification => Send to customer => SMS
   *
   * @param {string[]} parentTree - Array representing the current path of menu items leading to the hovered item.
   * @param {string} id - The ID of the currently hovered menu item.
   */

  const menuMoveHandle = useCallback((parentTree: string[], id: string) => {
    setHoveredItemTree((prevTree) => {
      const newTree = [...parentTree, id];
      if (
        prevTree.length === newTree.length &&
        prevTree.every((item, index) => item === newTree[index])
      ) {
        return prevTree;
      }
      return newTree;
    });
  }, []);

  /**
   * Displays the submenu of a context menu item when hovered, if a submenu exists.
   *
   * @param {IContextMenuItems} item - The context menu item being hovered over, which may contain a submenu.
   */

  const menuHoverEvent = useCallback((item: IContextMenuItems) => {
    if (item.subMenu) {
      const element = document.getElementById(`submenu-${item.key}`) as HTMLElement;
      element.classList.add('show-submenu');
      element.classList.remove('hide-submenu');
    }
  }, []);

  const menuHoverOutEvent = useCallback((key: string) => {
    const element = document.getElementById(`submenu-${key}`) as HTMLElement;

    if (element) {
      element.classList.add('hide-submenu');
      element.classList.remove('show-submenu');
    }
    setHoveredItemTree([]);
  }, []);

  const closeContextMenu = () => {
    if (contextMenuRef.current) {
      contextMenuRef.current.style.opacity = '0';
      contextMenuRef.current.style.visibility = 'hidden';
      contextMenuRef.current.classList.add('hide-context-menu');
    }
  };

  const handleClick = useCallback(
    async (event: React.MouseEvent, item: IContextMenuItems) => {
      event.stopPropagation();
      if (item.onClick && contextMenuEvent) {
        await item.onClick({ item, closeContextMenu, rowData: contextMenuEvent.data });
      }
      closeContextMenu();
    },
    [contextMenuEvent]
  );

  /**
   * Renders the appropriate icon for a context menu item based on its type.
   * Supports string, React elements, and React components as icon types.
   *
   * @param {IContextMenuItems} item - The context menu item, potentially containing an icon.
   * @param {string} txtId - will add hover effect on the icon.
   * @returns {JSX.Element} - The rendered icon or an empty fragment if no icon is provided.
   */

  const renderMenuIcon = (item: IContextMenuItems, txtId: string) => {
    if (typeof item.icon === 'string' || React.isValidElement(item.icon)) {
      return item.icon;
    }
    if (typeof item.icon === 'function' || typeof item.icon === 'object') {
      const IconComponent = item.icon as React.ElementType;

      return <IconComponent bool={hoveredItemTree.includes(txtId)} />;
    }

    return <></>;
  };

  const renderContextMenuItems = (
    items: IContextMenuItems[] = [],
    level = 0,
    parentTree: string[] = []
  ) => {
    return items.map((item) => {
      // txtId will be added to the submenu hovered item tree
      const txtId = `menuTxt-${item.key}-${level}`;
      const currentTree = [...parentTree, txtId];

      return (
        <div
          key={item.key}
          className={`menu-option ${item.disabled && item.disabled(contextMenuEvent as CellContextMenuEvent) && 'opacity-50 cursor-not-allowed'}`}
          onMouseMove={(e) => {
            e.stopPropagation();
            menuMoveHandle(currentTree, txtId);
          }}
          onMouseOver={() => menuHoverEvent(item)}
          onMouseOut={() => menuHoverOutEvent(item.key)}
          onLoad={() => item.disabled && item.disabled(contextMenuEvent as CellContextMenuEvent)}
          onClick={
            item.disabled && item.disabled(contextMenuEvent as CellContextMenuEvent)
              ? undefined
              : (event) => !item.subMenu && handleClick(event, item)
          }
        >
          <div
            aria-disabled={item.disabled && item.disabled(contextMenuEvent as CellContextMenuEvent)}
            className={`option-label-wrapper ${!item.icon && level < 2 && 'ml-[28px]'} ${
              hoveredItemTree.includes(txtId) ? 'text-primary-500' : ''
            } `}
            id={txtId}
          >
            <span>{item.icon && renderMenuIcon(item, txtId)}</span>
            {item.label}
          </div>
          {item.subMenu && <img src={subMenuSvg} alt=">" />}

          {item.subMenu &&
            (!item.disabled || !item.disabled(contextMenuEvent as CellContextMenuEvent)) && (
              <div className={`submenu-wrapper submenu`} id={`submenu-${item.key}`}>
                {renderContextMenuItems(item.subMenu, level + 1, currentTree)}
              </div>
            )}
        </div>
      );
    });
  };

  return (
    <div
      className="context-menu-wrapper hide-context-menu"
      id="gridContextMenu"
      ref={contextMenuRef}
      style={{ transition: '.2s' }}
    >
      {renderContextMenuItems(menuItems, 1, [])}
    </div>
  );
};

export default memo(GridContextMenu);
