import { LumigoLogo } from '@/assets';
import { ROUTES } from '@/constant/RoutesConstant';
import { useConfig } from '@/contexts/ConfigContext';
import { useLanguage } from '@/hooks/useLanguage';
import React from 'react';

export const AuthLayout: React.FC<React.PropsWithChildren<object>> = ({ children }) => {
  const { t } = useLanguage();
  const { config } = useConfig();

  return (
    <div className="min-h-screen bg-primary-25 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8 gap-8">
      <div className="flex gap-8 mt-8 flex-col sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center items-center flex-col gap-4">
          <img src={LumigoLogo} alt="Transport App Logo" className="h-[41px]" />
          <h1 className="text-[30px] font-semibold text-[#20363F]">
            {window.location.pathname === ROUTES.COMMON.LOGIN
              ? t('auth.loginTitle', { appName: config.appName as string })
              : config.appName}
          </h1>
        </div>
        <div className="login-form-wrapper bg-white py-8 px-4 rounded-[18px] sm:pt-9 sm:pb-10 sm:px-12 border border-[#CDD7DB]">
          {children}
        </div>
      </div>
      <div className="links-wrapper flex justify-center items-center gap-8 text-sm">
        <div className="flex justify-center max-w-[450px] w-full">
          <div className="flex gap-2">
            <span className="cursor-pointer">{t('auth.footer.terms')}</span>
            <span className="cursor-pointer">{t('auth.footer.privacy')}</span>
            <span className="cursor-pointer">{t('auth.footer.docs')}</span>
            <span className="cursor-pointer">{t('auth.footer.helps')}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
