.main-container {
  @apply pt-5;
}
.column-manager-button {
  @apply !w-[40px] h-[40px] border-[1px] rounded-[8px];
}
.manage-columns-container {
  @apply p-[14px] w-[230px] h-[566px] bg-white rounded-lg z-10;
  @apply md:w-[325px];
  @apply gap-[14px] flex flex-col  lg:w-[566px];
}
.manage-columns-header {
  @apply flex gap-[8px];
}
.column-manage-title {
  @apply text-[14px] font-[600];
}
.inside-content-container {
  @apply space-y-2;
}
.input-wrapper {
  @apply relative;
}
.search-input {
  @apply lg:!w-[538px] 3xsm:w-[100%] h-[40px] border-[1px] rounded-[8px];
}
.uncheck-info-text {
  @apply text-sm text-gray-500 flex justify-start p-1;
}
.scroll-container {
  @apply overflow-y-auto h-[362px] border-[1px] rounded-[8px] py-[10px] px-[12px] gap-[10px];
}
.select-all-container {
  @apply flex items-center p-[2px];
}
.select-all-checkbox {
  @apply rounded border-gray-300;
}
.select-all-label {
  @apply text-sm text-gray-700 pl-2 cursor-pointer;
}
.dragging-item {
  @apply bg-gray-100;
}
.reorder-item {
  @apply p-[2px] flex items-center justify-between  rounded-md cursor-pointer overflow-y-auto;
}
.checkbox-label-container {
  @apply flex items-center;
}
.manage-columns-container::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 15px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
}

.select-all-checkbox > .ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--primary-600) !important;
  border-color: var(--primary-600) !important;
}

.all-checkboxes > .ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--primary-600) !important;
  border-color: var(--primary-600) !important;
}
.all-checkboxes {
  @apply rounded;
}
.column-label {
  @apply text-sm text-gray-700;
}
.column-menu-button {
  @apply cursor-pointer border-none text-black;
}

.non-draggable-group .reorder-item {
  @apply cursor-default;
}

.non-draggable-group .column-menu-button {
  @apply cursor-not-allowed opacity-50;
}
.button-container {
  @apply p-3  w-[215px] md:w-full flex gap-[14px] justify-end;
}
.reset-button {
  @apply py-2 px-4 border-[1px] border-primary-200 hover:!text-gray-700 hover:!bg-white hover:!border-primary-200 text-gray-700 rounded-md bg-white;
}
.apply-button {
  @apply border-none py-2 px-4 bg-primary-600 text-white rounded-md hover:!bg-primary-600 hover:!text-white  hover:border-none;
}
.no-columns-message {
  @apply text-center text-gray-500 text-base;
}
.column-manager-button-active {
  @apply border-[1px] border-primary-600;
  @apply !w-[40px] h-[40px] border-[1px] rounded-[8px];
}
