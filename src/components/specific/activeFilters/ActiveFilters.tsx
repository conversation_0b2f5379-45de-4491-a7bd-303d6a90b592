import { useLanguage } from '@/hooks/useLanguage';
import { IActiveFiltersProps } from './activeFiltersTypes';
import { Tag } from 'antd';
import { ClearCloseIcon } from '@/assets';

const ActiveFilters = (props: IActiveFiltersProps) => {
  const { selectedQuickFilterData, colDefs, clearAllToDefault } = props;
  const { t } = useLanguage();
  return (
    <>
      {selectedQuickFilterData?.length > 0 && (
        <div className="w-full flex pt-3 gap-2 items-center max-w-[95%] flex-wrap">
          <span className="text-[14px] text-[#20363F] font-[500]">
            {t('ordersPage.appliedFilter')}:
          </span>
          {selectedQuickFilterData.map((filter) => {
            const matchingColumn = colDefs.find((col) => col.field === filter.field);
            return (
              <Tag key={filter.field} className="flex gap-2 h-[32px] items-center">
                <span className="text-primary-600 text-[14px]">
                  {matchingColumn ? matchingColumn.headerName : filter.label}:
                </span>
                {Array.isArray(filter.value) ? (
                  <>
                    <span className="text-[14px]">{filter.value[0]}</span> {'=>'}
                    <span className="text-[14px]">{filter.value[1]}</span>
                  </>
                ) : (
                  <span className="text-[14px]">{filter.value}</span>
                )}
              </Tag>
            );
          })}
          <Tag
            onClick={() => clearAllToDefault()}
            className="flex gap-2 h-[32px] items-center cursor-pointer"
          >
            <img src={ClearCloseIcon} className="w-[20px] h-[20px]" />
            <span className="text-[14px]">{t('searchFilterBox.clearAll')}</span>
          </Tag>
        </div>
      )}
    </>
  );
};

export default ActiveFilters;
