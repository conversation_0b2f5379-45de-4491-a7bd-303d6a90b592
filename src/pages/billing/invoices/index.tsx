import { DeleteIcon, deleteSvg, EmailCredentialIcon, EyeIcon, PlusButtonIcon } from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import ColumnManage from '@/components/specific/columnManage';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { ROUTES } from '@/constant/RoutesConstant';
import { GridNames } from '@/types/AppEvents';
import { Button, Divider } from 'antd';
import { Link, useNavigate } from 'react-router-dom';

import { IColDef } from '@/types/AgGridTypes';
import Icon from '@ant-design/icons';
import { useCallback, useMemo, useRef, useState } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { defaultPagination } from '@/constant/generalConstant';
import { IAssignedFilters } from '@/pages/logistics/orders/orders.types';
import { advanceFilterObjectMapper, maskQuickFilterData } from '@/lib/SearchFilterTypeManage';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { on } from '@/contexts/PulseContext';
import { useLanguage } from '@/hooks/useLanguage';
import { DownloadIcon } from '@/assets/icons/downloadIcon';
import { IContextMenuItems } from '@/types/ContextMenuTypes';
import { ICellRendererParams } from 'ag-grid-community';

const staticData = [
  {
    id: 'inv_7k3m9p2x8q',
    invoiceNumber: 'INV-2023-001',
    status: 'Void invoice',
    dateSubmitted: '13/02/2020',
    companyName: 'Cunningham, Simon and Chen',
    contactName: 'Shari Allen',
    dueDate: '09/03/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 2309.79,
    aging: 1904,
  },
  {
    id: 'inv_4r8n5w1z6v',
    invoiceNumber: 'INV-2023-002',
    status: 'Draft',
    dateSubmitted: '28/01/2020',
    companyName: 'Nguyen and Sons',
    contactName: 'Ian Frank',
    dueDate: '21/03/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 7432.61,
    aging: 1892,
  },
  {
    id: 'inv_9e2h7c4m3l',
    invoiceNumber: 'INV-2023-003',
    status: 'Unpaid',
    dateSubmitted: '16/11/2020',
    companyName: 'Austin PLC',
    contactName: 'Mr. Samuel Steele',
    dueDate: '01/12/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 5945.88,
    aging: 1637,
  },
  {
    id: 'inv_6y1d8s5p9r',
    invoiceNumber: 'INV-2023-004',
    status: 'Draft',
    dateSubmitted: '25/03/2020',
    companyName: 'Gibson, Moore and Delacruz',
    contactName: 'Tim Martinez',
    dueDate: '05/04/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 1027.58,
    aging: 1877,
  },
  {
    id: 'inv_3f7j2k8t4n',
    invoiceNumber: 'INV-2023-005',
    status: 'Partially paid',
    dateSubmitted: '30/04/2020',
    companyName: 'Bryant and Sons',
    contactName: 'Carol Guerrero',
    dueDate: '11/06/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 6059.99,
    aging: 1810,
  },
  {
    id: 'inv_8w4m6x9b2q',
    invoiceNumber: 'INV-2023-006',
    status: 'Unpaid',
    dateSubmitted: '14/10/2020',
    companyName: 'Hamilton, Grimes and Jacobs',
    contactName: 'Alice Thompson',
    dueDate: '05/11/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 7188.59,
    aging: 1663,
  },
  {
    id: 'inv_5g3z7l1h8u',
    invoiceNumber: 'INV-2023-007',
    status: 'Unpaid',
    dateSubmitted: '07/12/2020',
    companyName: 'Hill-Davenport',
    contactName: 'Cassandra Rios',
    dueDate: '12/01/2021',
    recipientEmail: '<EMAIL>',
    totalAmount: 2282.36,
    aging: 1595,
  },
  {
    id: 'inv_2v9k4a6j7c',
    invoiceNumber: 'INV-2023-008',
    status: 'Overpaid',
    dateSubmitted: '09/05/2020',
    companyName: 'Garcia-Kim',
    contactName: 'Scott Bass',
    dueDate: '19/05/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 7612.19,
    aging: 1833,
  },
  {
    id: 'inv_1t5s8f3n6y',
    invoiceNumber: 'INV-2023-009',
    status: 'Paid',
    dateSubmitted: '14/10/2020',
    companyName: 'Adams and Sons',
    contactName: 'Elijah Russo',
    dueDate: '07/12/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 4283.88,
    aging: 1631,
  },
  {
    id: 'inv_7p2d4w9x5k',
    invoiceNumber: 'INV-2023-010',
    status: 'Overpaid',
    dateSubmitted: '09/03/2020',
    companyName: 'Petty, Rocha and Garcia',
    contactName: 'David Cooper',
    dueDate: '28/03/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 2231.61,
    aging: 1885,
  },
  {
    id: 'inv_4h8r6m1b3z',
    invoiceNumber: 'INV-2023-011',
    status: 'Overdue',
    dateSubmitted: '17/08/2020',
    companyName: 'Holland, Hogan and Johnston',
    contactName: 'Kellie Shelton',
    dueDate: '02/09/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 1018.18,
    aging: 1727,
  },
  {
    id: 'inv_9q7l3e5v8j',
    invoiceNumber: 'INV-2023-012',
    status: 'Sent',
    dateSubmitted: '03/04/2020',
    companyName: 'Simpson-Webster',
    contactName: 'Heather Fleming',
    dueDate: '05/05/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 8490.19,
    aging: 1847,
  },
  {
    id: 'inv_6c2u9f4n7t',
    invoiceNumber: 'INV-2023-013',
    status: 'Overpaid',
    dateSubmitted: '06/02/2020',
    companyName: 'Arnold, Silva and Gardner',
    contactName: 'Benjamin Guzman',
    dueDate: '18/02/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 7324.34,
    aging: 1924,
  },
  {
    id: 'inv_3k8s5p1x6w',
    invoiceNumber: 'INV-2023-014',
    status: 'Unpaid',
    dateSubmitted: '11/06/2020',
    companyName: 'Davenport-Rice',
    contactName: 'Dakota Schultz',
    dueDate: '28/06/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 9733.85,
    aging: 1793,
  },
  {
    id: 'inv_8m4y7g2h9d',
    invoiceNumber: 'INV-2023-015',
    status: 'Void invoice',
    dateSubmitted: '08/11/2020',
    companyName: 'Johnson-Price',
    contactName: 'Randy Collins',
    dueDate: '23/11/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 5565.2,
    aging: 1645,
  },
  {
    id: 'inv_5a1z6r8q4l',
    invoiceNumber: 'INV-2023-016',
    status: 'Overdue',
    dateSubmitted: '17/05/2020',
    companyName: 'Shah, Collins and Hancock',
    contactName: 'Cynthia Rhodes',
    dueDate: '02/07/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 2003.66,
    aging: 1789,
  },
  {
    id: 'inv_2j9v3b7f5c',
    invoiceNumber: 'INV-2023-017',
    status: 'Sent',
    dateSubmitted: '14/09/2020',
    companyName: 'Raymond Ltd',
    contactName: 'Emily Becker',
    dueDate: '26/09/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 6646.51,
    aging: 1703,
  },
  {
    id: 'inv_7u6n4t8k2m',
    invoiceNumber: 'INV-2023-018',
    status: 'Overpaid',
    dateSubmitted: '19/01/2020',
    companyName: 'Turner LLC',
    contactName: 'Wesley Fritz',
    dueDate: '03/02/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 8567.65,
    aging: 1939,
  },
  {
    id: 'inv_1o5x9i3s7p',
    invoiceNumber: 'INV-2023-019',
    status: 'Sent',
    dateSubmitted: '09/05/2020',
    companyName: 'Bell-Neal',
    contactName: 'Jorge Gibbs',
    dueDate: '12/06/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 2851.94,
    aging: 1809,
  },
  {
    id: 'inv_4w2e8h6r1v',
    invoiceNumber: 'INV-2023-020',
    status: 'Overdue',
    dateSubmitted: '21/04/2020',
    companyName: 'Fisher-Wallace',
    contactName: 'Kelsey Turner',
    dueDate: '11/05/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 3764.79,
    aging: 1841,
  },
  {
    id: 'inv_9l7b5q3j8n',
    invoiceNumber: 'INV-2023-021',
    status: 'Partially paid',
    dateSubmitted: '15/10/2020',
    companyName: 'Hunter-Dennis',
    contactName: 'Ashlee Williams',
    dueDate: '06/12/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 2743.08,
    aging: 1632,
  },
  {
    id: 'inv_6d8f4g2a9y',
    invoiceNumber: 'INV-2023-022',
    status: 'Payment failed',
    dateSubmitted: '18/01/2020',
    companyName: 'Foster, Spence and Ross',
    contactName: 'Keith Hunt MD',
    dueDate: '06/03/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 6386.29,
    aging: 1907,
  },
  {
    id: 'inv_3r1k7z5m6u',
    invoiceNumber: 'INV-2023-023',
    status: 'Overdue',
    dateSubmitted: '24/09/2020',
    companyName: 'Martinez-Harrison',
    contactName: 'Tammie Robinson',
    dueDate: '19/11/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 2523.63,
    aging: 1649,
  },
  {
    id: 'inv_8c4v2t9w7x',
    invoiceNumber: 'INV-2023-024',
    status: 'Payment failed',
    dateSubmitted: '19/03/2020',
    companyName: 'Johnson, Booth and Simpson',
    contactName: 'Sandra Robinson',
    dueDate: '22/04/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 2772.48,
    aging: 1860,
  },
  {
    id: 'inv_5s9p6i3h8q',
    invoiceNumber: 'INV-2023-025',
    status: 'Overdue',
    dateSubmitted: '03/02/2020',
    companyName: 'Hodges Group',
    contactName: 'Steven Woodard',
    dueDate: '27/02/2020',
    recipientEmail: '<EMAIL>',
    totalAmount: 6877.68,
    aging: 1915,
  },
];

const InvoiceGrid = () => {
  const { t } = useLanguage();
  const [searchText, setSearchText] = useState('');
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  // const [cellData, setCellData] = useState<CellContextMenuEvent<ICellRendererParams>>(
  //   {} as CellContextMenuEvent<ICellRendererParams>
  // );
  const gridRef = useRef<AgGridReact<any>>(null);
  const searchHandler = useCallback((value: string) => {
    setSearchText(value);

    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });
  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );
  const customerContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: 'Download',
        icon: DownloadIcon as React.ElementType,
        key: 'download',
      },
      {
        label: 'Print',
        icon: EmailCredentialIcon as React.ElementType,
        key: 'print',
        onClick: () => {
          window.print();
        },
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} alt="delete" />) as unknown as React.ElementType,
        key: 'delete',
      },
    ];
  }, [t]);
  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.invoicesGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });
  const navigate = useNavigate();
  const invoiceColDefs: IColDef[] = [
    {
      headerName: 'Invoice Number',
      field: 'invoiceNumber',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Status',
      flex: 1,
      field: 'status',
      visible: true,
      unSortIcon: true,
      cellRenderer: (params: { value: string }) => {
        // Or CellRendererParams<any, string>
        switch (params.value) {
          case 'Draft':
            return (
              <div className="flex justify-center">
                <span className="grey-chip block">Draft</span>
              </div>
            );
          case 'Sent':
            return (
              <div className="flex justify-center">
                <span className="primary-chip block w-full">Sent</span>
              </div>
            );
          case 'Paid':
            return (
              <div className="flex justify-center">
                <span className="success-chip block w-full">Paid</span>
              </div>
            );
          case 'Partially paid':
            return (
              <div className="flex justify-center">
                <span className="warning-chip block w-full">Partially paid</span>
              </div>
            );
          case 'Overpaid':
            // Assuming primary-chip is suitable for the blueish 'Overpaid' status in the image
            return (
              <div className="flex justify-center">
                <span className="primary-chip block w-full">Overpaid</span>
              </div>
            );
          case 'Overdue':
            return (
              <div className="flex justify-center">
                <span className="error-chip block w-full">Overdue</span>
              </div>
            );
          case 'Void invoice':
            // Using error-chip for 'Void invoice' as it's a terminal, often non-positive status, and red in image
            return (
              <div className="flex justify-center">
                <span className="error-chip block w-full">Void invoice</span>
              </div>
            );
          case 'Payment failed':
            return (
              <div className="flex justify-center">
                <span className="error-chip block w-full">Payment failed</span>
              </div>
            );
          case 'Unpaid':
            return (
              <div className="flex justify-center">
                <span className="error-chip block w-full">Unpaid</span>
              </div>
            );
          default:
            // For statuses not in the image (e.g., "Unpaid", "Payment failed", "Overdue"," from your staticData)
            // or any other status not explicitly handled.
            return params.value;
        }
      },
      minWidth: 150,
    },
    {
      headerName: 'Date Submitted',
      field: 'dateSubmitted',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Company Name',
      field: 'companyName',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Contact Name',
      field: 'contactName',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Due Date',
      field: 'dueDate',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Recipient Email',
      field: 'recipientEmail',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Total Amount',
      field: 'totalAmount',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Aging',
      field: 'aging',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Action',
      field: 'action',
      visible: true,
      unSortIcon: true,
      flex: 1,
      pinned: 'right',
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <div className="flex justify-center gap-2">
            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view"
              value={'view'}
              onClick={() =>
                navigate(ROUTES.BILLING.BILLING_EDIT_INVOICE.replace(':id', params.data.id))
              }
            />

            <Icon component={DeleteIcon} className="cursor-pointer" alt="delete" />
          </div>
        );
      },
      width: 90,
    },
  ];
  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col overflow-hidden bg-white">
        <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
          <div className="md:w-1/3 flex flex-col 3xsm:w-full">
            <PageHeadingComponent title={'Invoices'} />
          </div>
          <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
            <div className="flex gap-3">
              <SearchFilterComponent
                onSearch={triggerSearch}
                colDefs={invoiceColDefs}
                isSetQuickFilter={false}
                searchInputPlaceholder={'Search Invoice'}
                onFilterApply={applyFilters}
                setSelectedQuickFilterData={setSelectedQuickFilterData}
                supportedFields={filterableModules.invoices.advanceFilter}
                clearAllFunctionRef={clearAllFunctionRef}
                setFilterParams={setFilterParams}
              />

              <ColumnManage colDefs={invoiceColDefs} gridName={GridNames.invoicesGrid} />
            </div>
            <div className="pt-5">
              <Divider type="vertical" className="hidden md:flex h-[40px] !m-0" />
            </div>
            <div className="pt-0 md:pt-5">
              <Link to={ROUTES.BILLING.BILLING_CREATE_INVOICE}>
                <Button
                  className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                  icon={<PlusButtonIcon />}
                >
                  Create Invoice
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <ActiveFilters
          selectedQuickFilterData={[]}
          clearAllToDefault={() => {}}
          colDefs={invoiceColDefs}
        />
        <main className=" h-screen overflow-x-hidden overflow-y-auto bg-white">
          <div className="mx-auto pr-6 py-5 flex justify-center items-center">
            <CustomAgGrid
              isContextMenu
              gridName={GridNames.invoicesGrid}
              rowData={staticData}
              columnDefs={invoiceColDefs}
              gridId="gridWrapperForChildren"
              gridRef={gridRef}
              contextMenuItem={customerContextMenuItems}
              onContextMenu={() => {}}
              emptyState={{
                title:
                  searchText || selectedQuickFilterData.length > 0
                    ? t('common.noMatchesFound')
                    : 'No Invoices Found',
                description:
                  searchText || selectedQuickFilterData.length > 0 ? '' : 'To get started',
                link: searchText || selectedQuickFilterData.length > 0 ? '' : 'Add new invoice',
                onLinkAction: () => {},
              }}
            />
          </div>
        </main>
      </div>
    </div>
  );
};

export default InvoiceGrid;
