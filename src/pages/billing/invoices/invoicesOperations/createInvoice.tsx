import { customerHook } from '@/api/customer/useCustomer';
import { IResponseOrderDto } from '@/api/orders/order.types';
import { orderServiceHook } from '@/api/orders/useOrders';
import { CollapseUpIcon, DateCalendarIcon, EyeIcon, RefreshInvoiceIcon } from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import TransferGrid from '@/components/common/transferGrid/TransferGrid';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { ROUTES } from '@/constant/RoutesConstant';
import { IColDef } from '@/types/AgGridTypes';
import Icon from '@ant-design/icons';
import { ICellRendererParams } from 'ag-grid-community';
import { Button, Card, DatePicker, Form, InputNumber, Select } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import '../invoice.css';

const CreateInvoiceComponent = () => {
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const [customerOrderListing, setCustomerOrderListing] = useState<IResponseOrderDto[]>([]);
  const { data: orderListingByCustomerId } = orderServiceHook.useEntity(
    `customer/${selectedCustomer}`,
    {
      enabled: Boolean(selectedCustomer),
    }
  );
  const [isStatus, setIsStatus] = useState<{
    isCreate: boolean;
    isView: boolean;
    isEdit: boolean;
  }>({
    isCreate: true,
    isView: false,
    isEdit: false,
  });
  const { id } = useParams<{ id: string }>();
  const { data: allCustomerList } = customerHook.useEntities('all/minimal');
  const [customerListing, setCustomerListing] = useState<
    {
      value: string;
      label: JSX.Element;
    }[]
  >([]);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  useEffect(() => {
    if (allCustomerList) {
      const formattedResponse = allCustomerList.data.map((item) => ({
        value: item.id,
        label: (
          <span>
            {item.companyName} - {item.contactName}
          </span>
        ),
      }));
      setCustomerListing(formattedResponse);
    }
  }, [allCustomerList]);

  useEffect(() => {
    if (id) {
      setIsStatus({
        isCreate: false,
        isView: true,
        isEdit: false,
      });
    }
  }, [id]);
  useEffect(() => {
    if (orderListingByCustomerId) {
      setCustomerOrderListing(orderListingByCustomerId.data);
    }
  }, [orderListingByCustomerId]);
  const onCustomerSelect = (id: string) => {
    setSelectedCustomer(id);
  };
  const ordersColumns: IColDef[] = [
    {
      headerName: 'Tracking ID',
      field: 'trackingNumber',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Service Level',
      flex: 1,
      field: 'serviceLevel',
      visible: true,
      unSortIcon: true,
      minWidth: 150,
    },
    {
      headerName: 'Amount',
      field: 'amount',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Completed date',
      field: 'completedDate',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Status',
      field: 'status',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: 'Action',
      field: 'action',
      visible: true,
      unSortIcon: true,
      flex: 1,
      pinned: 'right',
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <div className="flex justify-center gap-2">
            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view"
              value={'view'}
              onClick={() =>
                navigate(
                  ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':id', params.data.id).replace(
                    ':tab',
                    'general'
                  )
                )
              }
            />
          </div>
        );
      },
      width: 90,
    },
  ];
  return (
    <div className="flex flex-col">
      <PageHeadingComponent
        title={
          isStatus.isEdit ? 'Edit Invoice' : isStatus.isView ? 'View Invoice' : 'Create Invoice'
        }
        isChildComponent
        onBackClick={() => window.history.back()}
        children={
          <Form.Item className="p-3 mt-4 !mb-0">
            {isStatus.isView ? (
              <span>Draft</span>
            ) : (
              <Select
                className="w-[200px] h-[40px]"
                defaultValue={'1'}
                options={[
                  {
                    value: '1',
                    label: 'Draft',
                  },
                  {
                    value: '2',
                    label: 'Sent',
                  },
                  {
                    value: '3',
                    label: 'Paid',
                  },
                ]}
              />
            )}
          </Form.Item>
        }
      />

      <div className="invoice-create-content mr-10">
        <Form layout="vertical" form={form}>
          <div className="first-part-header w-full flex gap-10 flex-col md:flex-row">
            <Form.Item className="w-[35%]" label="Customer">
              <Select
                options={customerListing}
                onSelect={(e) => {
                  onCustomerSelect(e);
                }}
                className="h-[40px]"
                placeholder="Select customer"
                suffixIcon={<CollapseUpIcon />}
              />
            </Form.Item>
            <div className="flex gap-10 w-[70%]">
              <Form.Item className="w-[35%]" label="Invoice Date">
                <DatePicker className="w-full h-[40px]" suffixIcon={<DateCalendarIcon />} />
              </Form.Item>{' '}
              <Form.Item className="w-[30%]" label="Invoice Number">
                <InputNumber className="w-full h-[40px]" />
              </Form.Item>{' '}
              <Form.Item className="w-[30%]" label="Due Date">
                <DatePicker className="w-full h-[40px]" suffixIcon={<DateCalendarIcon />} />
              </Form.Item>
            </div>
            <div className="pl-2 pr-[2.5rem] mt-7">
              <Button className="h-[40px]">
                <RefreshInvoiceIcon />
              </Button>
            </div>
          </div>
          <div className="transfer-grid-invoice">
            {isStatus.isView ? (
              <CustomAgGrid
                gridId={GridIdConstant.GRID_WRAPPER_FOR_EXTRA_SMALL}
                columnDefs={[]}
                rowData={[]}
                className="border rounded-md"
              />
            ) : (
              <TransferGrid
                colDefs={ordersColumns}
                initialRowData={customerOrderListing}
                setIsEdit={() => {}}
                setSearchText={() => {}}
                searchText={{
                  searchTextForAssigned: '',
                  searchTextForAvailable: '',
                  searchTextForSelected: '',
                }}
                gridProps={{
                  columnDefs: [],
                  className: '!h-[50vh] lg:!h-[50vh] 3xl:!h-[50vh]',
                  gridId: GridIdConstant.GRID_WRAPPER_FOR_INVOICE,
                }}
                isSave={false}
                hideBackNavigation
                assignedServices={[]}
                availableGridEmptyStateDescription=""
                availableGridEmptyStateTitle={
                  selectedCustomer == '' ? 'Select customer to view orders' : 'No orders found'
                }
                selectedGridEmptyState={
                  selectedCustomer == ''
                    ? 'Add selected orders to invoice'
                    : 'Select order from the unbilled orders to add them here.'
                }
                mainHeaderTitle=""
                availableGridSearchPlaceholder="Search orders"
                selectedGridSearchPlaceholder="Search orders"
                preventEditModeOnSave={false}
                availableGridHeader="Unbilled Orders"
                selectedGridHeader="Invoice Order"
                isAdvanceFilter={true}
              />
            )}
          </div>
          <div className="second-part-footer flex gap-4 w-full mt-1">
            <div className="left-part-footer flex flex-col w-1/2 gap-5">
              <Form.Item className="w-[94%] !mb-0" label="Memo">
                <TextArea rows={4} placeholder="Write your note here..." />
              </Form.Item>
              {isStatus.isView ? (
                <div className="flex gap-4 w-full">
                  {' '}
                  <Button disabled className="h-[40px] w-[20%]">
                    Send
                  </Button>
                  <Button
                    className="h-[40px] w-[20%]"
                    onClick={() => setIsStatus({ isCreate: false, isView: false, isEdit: true })}
                  >
                    Edit invoice
                  </Button>
                  <Button className="h-[40px] w-[20%]">Download</Button>
                  <Button className="h-[40px] w-[20%]">Payment summary</Button>
                  <Button className="h-[40px] w-[20%]">Close</Button>
                </div>
              ) : (
                <div className="flex gap-4 w-full">
                  <Button className="h-[40px] w-[22%]">Save as draft</Button>
                  <Button className="h-[40px] w-[22%]">Save & Send</Button>
                  <Button className="h-[40px] w-[22%]">Preview</Button>
                  <Button className="h-[40px] w-[22%]">Discard & Close</Button>
                </div>
              )}
            </div>
            <div className="right-part-footer w-[47%] flex gap-7">
              <Card className="w-1/2 bg-[#F5F6FF]">
                <div className="flex  flex-col h-[130px]">
                  <span className="flex justify-between">
                    <span className="font-[600]">Total orders</span>
                    <span className="font-[600]">50</span>
                  </span>{' '}
                  <span className="flex justify-between">
                    <span className="font-[600]">Balance due</span>
                    <span className="font-[600]">$895.00</span>
                  </span>
                </div>
              </Card>
              <Card className="w-1/2 bg-[#F5F6FF]">
                <div className="flex  flex-col h-[130px]">
                  <span className="flex justify-between">
                    <span className="font-[600]">Subtotal</span>
                    <span className="font-[600]">$100.00</span>
                  </span>{' '}
                  <span className="flex justify-between">
                    <span className="font-[600]">GST</span>
                    <span className="font-[600]">$3.00</span>
                  </span>{' '}
                  <span className="flex justify-between">
                    <span className="font-[600]">QST</span>
                    <span className="font-[600]">$9.00</span>
                  </span>{' '}
                  <span className="flex justify-between">
                    <span className="font-[600]">Total amount</span>
                    <span className="font-[600]">$112.00</span>
                  </span>
                </div>
              </Card>
            </div>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default CreateInvoiceComponent;
