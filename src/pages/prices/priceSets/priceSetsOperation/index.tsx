import { TabsComponent } from '@/components/common/customTabs/CustomTabs';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import PageBreadCrumbsComponent from '@/components/specific/pageBreadCrumb/PageBreadCrumbComponent';
import { useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { Tooltip } from 'antd';
import PriceSetGeneral from './priceSetsGeneral';
import { logger } from '@/lib/logger/logger';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import BasePriceByZone from './basePriceByZone';
import PriceModifierComponent from './assignPriceModifiers';
import PriceSetCustomersComponent from './assignCustomers';
import Schedule from './schedule';
import { priceSetHook } from '@/api/priceSet/usePriceSet';
import { CreatePriceSetDto } from '@/api/priceSet/priceSet.types';
import PriceSetHistory from './priceSetHistory/priceSetHistory';

const PriceSetOperation = () => {
  const { id, tab } = useParams<{ id: string; tab: string }>();
  const { t } = useLanguage();
  const navigate = useNavigate();
  const isEditMode = Boolean(id);
  const { refetch: refetchPriceSets } = priceSetHook.useList({ pageNumber: 1, pageSize: 100 });
  const { data: priceSetData } = priceSetHook.useEntity(id as string, {
    enabled: isEditMode,
  });
  const updatePriceSetMutation = priceSetHook.useUpdate({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('priceSetPage.notification.successEdit'),
      });
    },
  });
  const notificationManager = useNotificationManager();
  const createPriceSetMutation = priceSetHook.useCreate({
    onSuccess: async () => {
      await refetchPriceSets();
      notificationManager.success({
        message: t('common.success'),
        description: t('priceSetPage.notification.successAdded'),
      });
    },
  });
  const onFinish = async (values: CreatePriceSetDto) => {
    try {
      if (id) {
        const sanitizedPayload: CreatePriceSetDto = {
          ...values,
          name: values?.name?.trimStart().trimEnd(),
          internalName: values?.internalName?.trimStart().trimEnd(),
          description: values?.description?.trimStart().trimEnd(),
          notes: values?.notes?.trimStart().trimEnd(),
        };

        await updatePriceSetMutation.mutateAsync({ id: id, data: sanitizedPayload });
      } else {
        const response = await createPriceSetMutation.mutateAsync(values);
        navigate(
          ROUTES.PRICES.PRICES_PRICE_SETS_EDIT.replace(':id', response.id).replace(
            ':tab',
            'general'
          ),
          {
            replace: true,
          }
        );
      }
    } catch (error) {
      logger.error('ERROR', error as Error);
    }
  };

  const defaultBreadCrumbItems = [
    {
      key: 'general',
      label: t('priceSetPage.tabs.general'),
      children: <PriceSetGeneral onFinish={onFinish} />,
      tabKey: 'General',
    },
    {
      key: 'schedule',
      label: id ? (
        t('priceSetPage.tabs.schedule')
      ) : (
        <Tooltip title={t('priceSetPage.tooltip.tabRestriction')}>
          {t('priceSetPage.tabs.schedule')}
        </Tooltip>
      ),
      children: <Schedule />,
      disabled: !id,
      tabKey: 'Schedule',
    },
    {
      key: 'basePriceByZone',
      label: id ? (
        t('priceSetPage.tabs.basePriceByZone')
      ) : (
        <Tooltip title={t('priceSetPage.tooltip.tabRestriction')}>
          {t('priceSetPage.tabs.basePriceByZone')}
        </Tooltip>
      ),
      children: <BasePriceByZone />,
      disabled: !id,
      tabKey: 'Base price by zone',
    },
    {
      key: 'priceModifiers',
      label: id ? (
        t('priceSetPage.tabs.priceModifier')
      ) : (
        <Tooltip title={t('priceSetPage.tooltip.tabRestriction')}>
          {t('priceSetPage.tabs.priceModifier')}
        </Tooltip>
      ),
      children: <PriceModifierComponent />,
      disabled: !id,
      tabKey: 'Price modifiers',
    },
    {
      key: 'customers',
      label: id ? (
        t('priceSetPage.tabs.customers')
      ) : (
        <Tooltip title={t('priceSetPage.tooltip.tabRestriction')}>
          {t('priceSetPage.tabs.customers')}
        </Tooltip>
      ),
      children: <PriceSetCustomersComponent />,
      disabled: !id,
      tabKey: 'Customers',
    },
    {
      key: 'history',
      label: id ? (
        t('priceSetPage.tabs.history')
      ) : (
        <Tooltip title={t('priceSetPage.tooltip.tabRestriction')}>
          {t('priceSetPage.tabs.history')}
        </Tooltip>
      ),
      children: <PriceSetHistory />,
      disabled: !id,
      tabKey: 'History',
    },
  ];

  const breadCrumbObj: { [key: string]: string } = useMemo(() => {
    return {
      general: t('priceSetPage.tabs.general'),
      schedule: t('priceSetPage.tabs.schedule'),
      basePriceByZone: t('priceSetPage.tabs.basePriceByZone'),
      priceModifiers: t('priceSetPage.tabs.priceModifier'),
      customers: t('priceSetPage.tabs.customers'),
      history: t('priceSetPage.tabs.history'),
    };
  }, [t]);

  const breadCrumbPath = useMemo(() => {
    const tabKey = tab || 'general';

    return [
      {
        name: id ? (priceSetData?.name as string) : t('priceSetPage.header.title'),
        path: ROUTES.PRICES.PRICES_PRICE_SETS,
      },
      {
        name: tab ? (breadCrumbObj[tab || 'general'] as string) : t('priceSetPage.tabs.general'),
        path: id
          ? ROUTES.PRICES.PRICES_PRICE_SETS_EDIT.replace(':id', id!).replace(':tab', tabKey)
          : ROUTES.PRICES.PRICES_PRICE_SETS_ADD.replace(':tab', tabKey),
      },
    ];
  }, [tab, id, priceSetData?.name, t, breadCrumbObj]);

  return (
    <div>
      <div className="flex flex-col gap-1 avoid-tab-position">
        <div>
          <PageHeadingComponent
            title={
              id ? t('priceSetPage.header.editPriceSet') : t('priceSetPage.header.addPriceSet')
            }
            isChildComponent
            onBackClick={() => navigate(ROUTES.PRICES.PRICES_PRICE_SETS)}
          />
          <PageBreadCrumbsComponent path={breadCrumbPath} />
        </div>
        <TabsComponent
          tabs={defaultBreadCrumbItems}
          editableRoute={ROUTES.PRICES.PRICES_PRICE_SETS_EDIT}
        />
      </div>
    </div>
  );
};
export default PriceSetOperation;
