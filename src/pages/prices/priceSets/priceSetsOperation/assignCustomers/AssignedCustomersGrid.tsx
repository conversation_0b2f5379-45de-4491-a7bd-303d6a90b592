import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { useCallback, useEffect, useState } from 'react';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { Button, Divider } from 'antd';
import { emptyStateIcon2, PlusButtonIcon } from '@/assets';
import { searchData } from '@/lib/helper';
import { useLanguage } from '@/hooks/useLanguage';
import { ICustomerServiceGridProps } from './assignedCustomer.types';
import { IGetAssignedCustomers } from '@/api/priceSet/assignedCustomers/assignedCustomers.types';
import { GridIdConstant } from '@/constant/GridIdConstant';

const AssignedCustomersGrid: React.FC<ICustomerServiceGridProps> = (props) => {
  const [services, setServices] = useState<IGetAssignedCustomers[]>();
  const [serviceFilter, setServiceFilter] = useState<IGetAssignedCustomers[]>([]);
  const { t } = useLanguage();
  const {
    setIsEdit,
    allCustomers,
    colDefs,
    setSearchText,
    searchText,
    noSCustomersAvailableInSystem,
  } = props;

  useEffect(() => {
    if (allCustomers) {
      setServices(allCustomers || []);
      setServiceFilter(allCustomers || []);
    }
  }, [allCustomers]);

  const searchHandler = useCallback(
    (value: string) => {
      const results = searchData(
        serviceFilter,
        {
          query: value,
        },
        colDefs
      );
      setSearchText((prev) => ({
        ...prev,
        searchTextForAssigned: value,
      }));
      setServices(results);
    },
    [colDefs, serviceFilter, setSearchText]
  );

  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);

  const displayNav =
    (services && services?.length > 0) ||
    (searchText.searchTextForAssigned && services?.length === 0);

  return (
    <div className="flex h-full">
      <div className="flex-1 gap-3 flex flex-col overflow-hidden bg-white">
        {displayNav && (
          <div className="flex items-end 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[24px] 3xsm:w-full 3xsm:gap-3">
            <div className="flex gap-3 justify-end">
              <SearchFilterComponent
                onSearch={triggerSearch}
                colDefs={colDefs}
                advanceFilter={false}
                searchInputPlaceholder={t('priceSetPage.customers.searchPlaceholder')}
              />
            </div>
            <Divider type="vertical" className="h-[40px] m-0" />
            <Button
              className="w-[168px] border-[1px] h-[40px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
              icon={<PlusButtonIcon />}
              type="primary"
              onClick={() => setIsEdit(true)}
            >
              {t('priceSetPage.customers.assignCustomersBtn')}
            </Button>
          </div>
        )}
        <main className="h-full overflow-x-hidden overflow-y-auto bg-white pr-6">
          <CustomAgGrid
            rowData={services}
            columnDefs={colDefs}
            gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
            pagination
            className="max-h-[75vh] !h-[75vh] lg:!h-[76vh]"
            emptyState={{
              title: searchText.searchTextForAssigned
                ? t('common.noMatchesFound')
                : noSCustomersAvailableInSystem
                  ? 'No customer Found '
                  : t('priceSetPage.customers.noCustomersAssigned'),
              description:
                searchText.searchTextForAssigned || noSCustomersAvailableInSystem
                  ? ''
                  : t('priceSetPage.customers.toGetStarted'),
              link:
                searchText.searchTextForAssigned || noSCustomersAvailableInSystem
                  ? ''
                  : 'click here.',
              image: searchText.searchTextForAssigned ? undefined : emptyStateIcon2,
              onLinkAction: () => setIsEdit(true),
            }}
          />
        </main>
      </div>
    </div>
  );
};
export default AssignedCustomersGrid;
