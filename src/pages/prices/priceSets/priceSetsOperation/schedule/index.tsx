import {
  CreateScheduleDto,
  IFormSchedules,
  IScheduleArray,
  IScheduleForm,
  OffsetType,
  PriceSetAvailability,
} from '@/api/priceSet/schedule/schedule.types';
import { scheduleServiceHook } from '@/api/priceSet/schedule/useSchedule';
import { DeleteIcon, PlusButtonIcon } from '@/assets';
import SelectDownArrow from '@/assets/icons/selectDownArrow';
import CustomDivider from '@/components/common/divider/CustomDivider';
import { useConfig } from '@/contexts/ConfigContext';
import { useLanguage } from '@/hooks/useLanguage';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { numberFieldValidator } from '@/lib/FormValidators';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Button, Form, Input, Select, TimePicker } from 'antd';
import { RuleObject } from 'antd/es/form';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useMemo } from 'react';
import { useParams } from 'react-router-dom';

const Schedule: React.FC = () => {
  const { config } = useConfig();
  const { t } = useLanguage();
  const notificationManager = useNotificationManager();

  const { id: priceSetId } = useParams();

  const availabilityOptions = useMemo(() => {
    return [
      {
        label: t('priceSetPage.schedule.availabilityOptions.never'),
        value: 'never',
      },
      {
        label: t('priceSetPage.schedule.availabilityOptions.always'),
        value: 'always',
      },
      {
        label: t('priceSetPage.schedule.availabilityOptions.weekly'),
        value: 'weekly',
      },
    ];
  }, [t]);

  const dayOptions = useMemo(() => {
    return [
      { label: 'Sunday', value: 'Sunday' },
      { label: 'Monday', value: 'Monday' },
      { label: 'Tuesday', value: 'Tuesday' },
      { label: 'Wednesday', value: 'Wednesday' },
      { label: 'Thursday', value: 'Thursday' },
      { label: 'Friday', value: 'Friday' },
      { label: 'Saturday', value: 'Saturday' },
    ];
  }, []);

  const offsetOptions = useMemo(() => {
    return [
      { label: t('priceSetPage.schedule.offsetTypeOptions.to'), value: 'to' },
      { label: t('priceSetPage.schedule.offsetTypeOptions.by'), value: 'by' },
    ];
  }, [t]);

  const weekendOptions = useMemo(() => {
    return [
      { label: t('priceSetPage.schedule.week.includes'), value: true },
      { label: t('priceSetPage.schedule.week.excludes'), value: false },
    ];
  }, [t]);

  const daysInitialValue = useMemo(() => dayOptions.map((day) => day.value), [dayOptions]);

  const { data: scheduleDetails } = scheduleServiceHook.useEntity(
    `${priceSetId}/schedule` as string,
    {
      enabled: Boolean(priceSetId),
    }
  );

  const [scheduleForm] = Form.useForm<IScheduleForm>();
  const scheduleFormWatcher = Form.useWatch([], scheduleForm);

  const handleAddSchedule = async (add: () => void) => {
    try {
      await scheduleForm.validateFields(['schedule'], { recursive: true });
      add();
    } catch {
      /* empty */
    }
  };

  useEffect(() => {
    if (!scheduleDetails) return;
    const { offsetType, offsetData, schedule } = scheduleDetails;

    scheduleForm.setFieldsValue({
      ...scheduleDetails,
      availabilityType: scheduleDetails?.availabilityType || availabilityOptions[0].value,
      offsetType: offsetType || OffsetType.TO,
      hours: String(offsetData?.hours ?? ''),
      minutes: String(offsetData?.minutes ?? ''),
      includeWeekends: offsetData?.includeWeekends === null || offsetData?.includeWeekends,
      time: offsetData?.time ? dayjs(offsetData.time, 'HH:mm') : undefined,
      daysOut: String(offsetData?.daysOut ?? ''),
      schedule: schedule?.length ? schedule.map(formatScheduleData) : undefined,
    });
  }, [availabilityOptions, priceSetId, scheduleDetails, scheduleForm]);

  const formatScheduleData = (scheduleItem: IScheduleArray) => ({
    ...scheduleItem,
    days: scheduleItem.days.split(','),
    time: [dayjs(scheduleItem.startTime, 'HH:mm:ss'), dayjs(scheduleItem.endTime, 'HH:mm:ss')],
  });

  const createMutation = scheduleServiceHook.useCreateById('schedule', priceSetId as string, {
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('priceSetPage.schedule.notification.scheduleSaved'),
      });
    },
  });

  const isMaximumSchedule =
    config && scheduleFormWatcher?.schedule
      ? scheduleFormWatcher?.schedule?.length === config?.maxPriceSetScheduleLimit
      : false;

  const onFinish = async (formValues: IScheduleForm) => {
    const timeFormat = 'HH:mm';

    const formatScheduleItem = ({ time, ...scheduleItem }: IFormSchedules): IScheduleArray => ({
      ...scheduleItem,
      days: scheduleItem.days.join(','),
      startTime: time?.[0]?.format(timeFormat),
      endTime: time?.[1]?.format(timeFormat),
    });

    const formattedSchedules: IScheduleArray[] =
      formValues?.schedule?.map(formatScheduleItem) || [];

    const schedulePayload: CreateScheduleDto = {
      availabilityType: formValues.availabilityType,
      offsetType: formValues.offsetType,
      offsetData: {
        daysOut: Number(formValues.daysOut) || null,
        hours: formValues.hours ? Number(formValues.hours) : null,
        minutes: formValues.minutes ? Number(formValues.minutes) : null,
        includeWeekends: formValues.includeWeekends,
        time: formValues.time ? formValues.time.format(timeFormat) : null,
      },
      schedule: formattedSchedules,
    };

    await createMutation.mutateAsync(schedulePayload);
  };

  const maxMinutesValidator = (_rule: RuleObject, value: string) => {
    if (Number(value) > 59) {
      return Promise.reject(t('priceSetPage.schedule.form.minuteLimitError'));
    }
    return Promise.resolve();
  };

  const dateAndTimeValidator = (_: RuleObject, values: [Dayjs, Dayjs]) => {
    if (dayjs(values[0]).isSame(dayjs(values[1]))) {
      return Promise.reject(t('priceSetPage.schedule.form.sameTimeError'));
    }
    return Promise.resolve();
  };

  return (
    <div className="h-full pr-6">
      <CustomDivider label="Schedule details" className="my-4" />
      <Form className="custom-form" layout="vertical" form={scheduleForm} onFinish={onFinish}>
        <div className="flex flex-col gap-4">
          <div className="flex gap-2.5 flex-col">
            <Form.Item
              name={'availabilityType'}
              label={t('priceSetPage.schedule.form.priceSetAvailability')}
              initialValue={availabilityOptions[0].value}
              required
            >
              <Select
                options={availabilityOptions}
                prefixCls="custom-select"
                className="w-full md:w-full"
                suffixIcon={<SelectDownArrow />}
              />
            </Form.Item>
          </div>
          {isMaximumSchedule && (
            <div className="flex gap-2 bg-primary-25 w-full rounded p-2 font-semibold mt-2 text-primary-500 select-none">
              <InfoCircleOutlined />
              {t('priceSetPage.schedule.form.maxScheduleLimitInfoMessage', {
                limit: String(config.maxPriceSetScheduleLimit),
              })}
            </div>
          )}
          {scheduleFormWatcher?.availabilityType === PriceSetAvailability.WEEKLY && (
            <div className="px-5 py-4 bg-primary-25 max-h-[50vh] overflow-y-scroll border border-primary-100 rounded-[14px]">
              <span className="block font-medium text-[#20363F] mb-2">
                {t('priceSetPage.schedule.form.days')}
              </span>
              <Form.List name="schedule" initialValue={[{ days: daysInitialValue }]}>
                {(fields, { add, remove }) => (
                  <>
                    <div className="w-full flex flex-col overflow-y-auto gap-4">
                      {fields?.map((field, index) => (
                        <div className="flex gap-4 items-start">
                          <Form.Item
                            name={[field.name, 'days']}
                            className="w-full"
                            initialValue={daysInitialValue}
                            rules={[
                              {
                                required: true,
                                message: t('priceSetPage.schedule.form.requiredDays'),
                              },
                            ]}
                          >
                            <Select
                              options={dayOptions}
                              prefixCls="custom-select"
                              className="w-full md:w-full"
                              mode="multiple"
                              suffixIcon={<SelectDownArrow />}
                            />
                          </Form.Item>
                          <Form.Item
                            name={[field.name, 'time']}
                            validateFirst
                            rules={[
                              {
                                required: true,
                                message: t('priceSetPage.schedule.form.requiredTime'),
                              },
                              {
                                validator: dateAndTimeValidator,
                              },
                            ]}
                          >
                            <TimePicker.RangePicker
                              showSecond={false}
                              prefixCls="custom-date-picker"
                              format={
                                config.is12HoursFormate
                                  ? config.timeFormate12
                                  : config.timeFormate24
                              }
                              placeholder={[
                                t('priceSetPage.schedule.form.startTime'),
                                t('priceSetPage.schedule.form.endTime'),
                              ]}
                            />
                          </Form.Item>
                          {
                            <Button
                              disabled={index <= 0}
                              className="p-2 h-[40px] hover:!border-[#d9d9d9]"
                              onClick={() => remove(index)}
                            >
                              <DeleteIcon />
                            </Button>
                          }
                        </div>
                      ))}
                      <Form.Item className="flex justify-end mb-0">
                        <Button
                          className="h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                          icon={<PlusButtonIcon />}
                          onClick={() => handleAddSchedule(add)}
                          disabled={isMaximumSchedule}
                        >
                          {t('priceSetPage.schedule.form.addScheduleBtn')}
                        </Button>
                      </Form.Item>
                    </div>
                  </>
                )}
              </Form.List>
            </div>
          )}
          {scheduleFormWatcher &&
            scheduleFormWatcher?.availabilityType !== PriceSetAvailability.NEVER && (
              <div className="flex flex-col gap-2">
                <label htmlFor="offsetDueDate" className="text-[#20363f] font-medium text-sm">
                  {t('priceSetPage.schedule.form.offsetDueDate')}
                </label>
                <div className="flex w-full gap-4 items-start">
                  <Form.Item
                    name={'offsetType'}
                    className="w-full max-w-[500px]"
                    initialValue={offsetOptions[0].value}
                  >
                    <Select
                      options={offsetOptions}
                      prefixCls="custom-select"
                      className="w-full md:w-full"
                      id="offsetDueDate"
                      suffixIcon={<SelectDownArrow />}
                    />
                  </Form.Item>
                  {scheduleFormWatcher?.offsetType === OffsetType.TO ? (
                    <>
                      <Form.Item
                        name="hours"
                        rules={[
                          {
                            required: true,
                            message: t('priceSetPage.schedule.form.requiredHours'),
                          },
                        ]}
                      >
                        <Input
                          addonAfter={t('priceSetPage.schedule.form.hours')}
                          maxLength={2}
                          onKeyDown={(event) => numberFieldValidator(event, {})}
                          placeholder={t('priceSetPage.schedule.form.hoursPlaceholder')}
                        />
                      </Form.Item>
                      <Form.Item
                        name="minutes"
                        rules={[
                          {
                            required: true,
                            message: t('priceSetPage.schedule.form.requiredMinutes'),
                          },
                          {
                            validator: maxMinutesValidator,
                          },
                        ]}
                      >
                        <Input
                          addonAfter={t('priceSetPage.schedule.form.minutes')}
                          maxLength={2}
                          onKeyDown={(event) => numberFieldValidator(event, {})}
                          placeholder={t('priceSetPage.schedule.form.minutesPlaceholder')}
                        />
                      </Form.Item>
                    </>
                  ) : (
                    <>
                      <Form.Item
                        name="time"
                        className="w-full min-w-[200px]"
                        rules={[
                          {
                            required: true,
                            message: t('priceSetPage.schedule.form.requiredHours'),
                          },
                        ]}
                      >
                        <TimePicker
                          showSecond={false}
                          prefixCls="custom-date-picker"
                          format={
                            config.is12HoursFormate ? config.timeFormate12 : config.timeFormate24
                          }
                        />
                      </Form.Item>
                      <Form.Item
                        name="daysOut"
                        className="w-full min-w-[200px]"
                        rules={[
                          {
                            required: true,
                            message: t('priceSetPage.schedule.form.requiredDaysOut'),
                          },
                        ]}
                      >
                        <Input
                          addonAfter={t('priceSetPage.schedule.form.daysOut')}
                          maxLength={2}
                          onKeyDown={(event) => numberFieldValidator(event, {})}
                          placeholder={t('priceSetPage.schedule.form.daysOutPlaceholder')}
                        />
                      </Form.Item>
                      <Form.Item
                        name={'includeWeekends'}
                        className="w-full"
                        initialValue={weekendOptions[0].value}
                      >
                        <Select
                          options={weekendOptions}
                          prefixCls="custom-select"
                          className="w-full md:w-full"
                          suffixIcon={<SelectDownArrow />}
                        />
                      </Form.Item>
                    </>
                  )}
                </div>
              </div>
            )}
        </div>
        <footer className="py-4">
          <Button
            loading={createMutation.isPending}
            className="h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
            htmlType="submit"
          >
            {t('common.save')}
          </Button>
        </footer>
      </Form>
    </div>
  );
};

export default Schedule;
