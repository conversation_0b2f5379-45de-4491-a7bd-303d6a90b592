import { infoCircleOutlined } from '@/assets';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { Button, Switch } from 'antd';
import './CustomerSettings.css';
import { useEffect, useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { SettingsKeys } from '@/constant/SettingsKeys';
import { useParams } from 'react-router-dom';
import { updateSetting, useGetSettings } from '@/api/settings/settings.service';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { SettingsScope } from '@/constant/SettingsScope';
const SettingsGeneralComponent = () => {
  const [isPrePaidChecked, setIsPrePaidChecked] = useState(false);
  const { id } = useParams<{ id: string }>();
  const { data: generalSettings, refetch: refetchGeneralSettings } = useGetSettings(
    SettingsKeys.customerGeneralSettings,
    id,
    {
      enabled: Boolean(!!id && SettingsKeys.customerGeneralSettings),
    }
  );
  const notificationManager = useNotificationManager();
  useEffect(() => {
    setIsPrePaidChecked(generalSettings?.value?.generalSetting?.prePaidOrders);
  }, [generalSettings]);
  const handleOnSaveGeneral = async () => {
    const formattedData = {
      userId: id,
      scope: SettingsScope.USER,
      key: SettingsKeys.customerGeneralSettings,
      value: {
        generalSetting: {
          prePaidOrders: isPrePaidChecked,
        },
      },
    };
    await updateSetting(generalSettings.id, formattedData);
    refetchGeneralSettings();
    notificationManager.success({
      message: t('common.success'),
      description: t('dashboard.customer.settings.general.generalSettingsUpdated'),
    });
  };
  const { t } = useLanguage();
  return (
    <div className="flex flex-col gap-[20px] p-4">
      <div className="flex flex-col gap-2">
        <span className="text-[14px] font-semibold">
          {t('dashboard.customer.settings.general.generalSettings')}
        </span>
        <span className="text-[14px] text-[#647A83]">
          {t('dashboard.customer.settings.general.commonSettingsDescription')}
        </span>
      </div>
      <div className="switch-container flex bg-primary-25 p-2 rounded-[8px] justify-between">
        <div className="flex gap-2">
          <span className="text-[14px] font-semibold">
            {t('dashboard.customer.settings.general.prepaidOrders')}
          </span>
          <CustomTooltip
            content={
              <div className="!max-w-[350px]">
                <span className="w-full">
                  {t('dashboard.customer.settings.general.prepaidOrdersInfo')}
                </span>
              </div>
            }
          >
            <img src={infoCircleOutlined} alt="info" />
          </CustomTooltip>
        </div>{' '}
        <Switch
          checked={isPrePaidChecked}
          onChange={() => setIsPrePaidChecked(!isPrePaidChecked)}
          className="general-order-switch"
        />
      </div>
      <div className="w-6/6 lg:w-4/6 2xl:w-2/6">
        <Button
          disabled={generalSettings?.value?.generalSetting?.prePaidOrders === isPrePaidChecked}
          className="bg-primary-600 w-6/6 md:w-4/6 lg:w-2/6 p-5  text-[#FFFFFF] hover:!bg-primary-600 hover:!text-[#FFFFFF] hover:!border-grey-50 disabled:!bg-[lightgrey] disabled:!text-[darkgrey] disabled:!border-[lightgrey]"
          htmlType="button"
          onClick={() => handleOnSaveGeneral()}
        >
          {t('dashboard.customer.settings.general.saveSettings')}
        </Button>
      </div>
    </div>
  );
};

export default SettingsGeneralComponent;
