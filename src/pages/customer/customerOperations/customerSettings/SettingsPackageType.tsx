import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { useLanguage } from '@/hooks/useLanguage';
import { IColDef } from '@/types/AgGridTypes';
import { GridNames } from '@/types/AppEvents';
import { AgGridReact } from 'ag-grid-react';
import { EyeIcon, HistoryIcon } from '@/assets';
import { useCallback, useMemo, useRef, useState } from 'react';
import Icon from '@ant-design/icons/lib/components/Icon';
import { IndexedDBStore } from '@/lib/indexedDB';
import { StoreName } from '@/lib/indexedDB/databaseConfig';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { ISettingsPackageType } from './Settings.types';
import CustomModal from '@/components/common/modal/CustomModal';
import { Button, Form } from 'antd';
import CustomDivider from '@/components/common/divider/CustomDivider';
import PackageTypeFormComponent from './PackageTypeForm';
import { ICellRendererParams } from 'ag-grid-community';

const packageTypeStore = new IndexedDBStore<ISettingsPackageType>(StoreName.packageType);

const SettingsPackageTypeComponent = () => {
  const [searchText, setSearchText] = useState('');
  const { t } = useLanguage();
  const [packageTypeForm] = Form.useForm();
  const gridRef = useRef<AgGridReact<ISettingsPackageType>>(null);
  const [packageTypeInfo, setPackageTypeInfo] = useState<ISettingsPackageType[]>([]);
  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  const [initialData, setInitialData] = useState<ISettingsPackageType>();

  const closeModalHandler = useCallback(() => {
    setIsOpenModal(false);
    packageTypeForm.resetFields();
  }, [packageTypeForm]);
  const handlePackageTypeView = (params: ICellRendererParams) => {
    setInitialData(params.data);
    packageTypeForm.setFieldsValue(params.data);
    setIsOpenModal(true);
  };
  const packageTypeColDefs: IColDef[] = [
    {
      field: 'packagingType',
      headerName: t('dashboard.customer.settings.settingsNotification.packagingType'),
      unSortIcon: true,
      tooltipField: 'packagingType',
      type: 'string',
      visible: true,
      cellRenderer: (params: { value: string }) => {
        return searchText ? highlightText(params.value, searchText) : params.value;
      },
      width: 315,
    },
    {
      field: 'visible',
      headerName: t('dashboard.customer.settings.settingsNotification.visible'),
      unSortIcon: true,
      tooltipField: 'visible',
      type: 'string',
      visible: true,
      cellRenderer: (params: ICellRendererParams) => {
        return params.data.visible ? (
          <div className="flex gap-3 h-full items-center">Visible</div>
        ) : (
          <div className="flex gap-3 h-full items-center">Not visible</div>
        );
      },
      width: 315,
    },
    {
      field: 'weight',
      headerName: t('dashboard.customer.settings.settingsNotification.weight'),
      unSortIcon: true,
      tooltipField: 'weight',
      type: 'string',
      visible: true,
      width: 145,
    },
    {
      field: 'maxWeight',
      headerName: t('dashboard.customer.settings.settingsNotification.maxWeight'),
      unSortIcon: true,
      tooltipField: 'maxWeight',
      type: 'string',
      visible: true,
      width: 140,
    },
    {
      field: 'width',
      headerName: t('dashboard.customer.settings.settingsNotification.width'),
      unSortIcon: true,
      tooltipField: 'width',
      type: 'string',
      visible: true,
      width: 140,
    },
    {
      field: 'height',
      headerName: t('dashboard.customer.settings.settingsNotification.height'),
      unSortIcon: true,
      tooltipField: 'height',
      type: 'string',
      visible: true,
      width: 140,
    },
    {
      field: 'length',
      headerName: t('dashboard.customer.settings.settingsNotification.length'),
      unSortIcon: true,
      tooltipField: 'length',
      type: 'string',
      visible: true,
      width: 140,
    },
    {
      field: 'action',
      headerName: t('vehiclePage.colDefs.action'),
      pinned: 'right',
      width: 80,
      visible: true,
      sortable: false,
      resizable: false,
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <div className="flex gap-2 h-full items-center">
            <CustomTooltip
              content={
                <div className="text-[#20363f] flex flex-col text-xs font-medium gap-1 w-full">
                  {t('common.added')}{' '}
                  <span className=" block w-fit text-sm font-semibold">01/11/21 02:25PM</span>
                  <hr className="border-[#0000001c]" />
                  {t('common.modified')}:{' '}
                  <span className=" block w-fit text-sm font-semibold">01/11/21 02:25PM</span>
                  <hr className="border-[#0000001c]" />
                  {t('common.lastUpdatedBy')}:{' '}
                  <span className="block w-fit text-sm font-semibold">Cameron Williamson</span>
                </div>
              }
              placement="leftTop"
            >
              <div>
                <Icon component={HistoryIcon} className="cursor-pointer mt-2" alt="history" />
              </div>
            </CustomTooltip>

            <Icon
              component={EyeIcon}
              onClick={() => handlePackageTypeView(params)}
              className="cursor-pointer"
              alt="view"
              value={'view'}
            />
          </div>
        );
      },
    },
  ];
  const loadPackageTypeInfo = useCallback(async () => {
    try {
      setLoading(true);
      const response = await packageTypeStore.getAll();
      setPackageTypeInfo(response);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }, []);
  const searchHandler = useCallback((value: string) => {
    setSearchText(value);
  }, []);
  const onFinish = async (e: ISettingsPackageType) => {
    if (!initialData?.id) return;
    setLoading(true);
    try {
      await packageTypeStore.update(initialData.id, e);
      loadPackageTypeInfo();
      setIsOpenModal(false);
    } finally {
      setLoading(false);
    }
  };
  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button className="rounded-lg border-primary-200" onClick={closeModalHandler}>
          {t('common.cancel')}
        </Button>
        <Button
          form="packageType"
          htmlType="submit"
          type="primary"
          loading={loading}
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
        >
          {t('common.save')}
        </Button>
      </footer>
    ),
    [closeModalHandler, loading, t]
  );

  return (
    <>
      <CustomModal
        modalTitle={t('dashboard.customer.settings.settingsNotification.editPackageTypeInfo')}
        modalDescription={t(
          'dashboard.customer.settings.settingsNotification.enterNewPackageTypeDetails'
        )}
        open={isOpenModal}
        footer={Footer}
        destroyOnClose
        keyboard={false}
        onCancel={closeModalHandler}
      >
        <CustomDivider label={t('dashboard.customer.settings.settingsNotification.basicDetails')} />
        <PackageTypeFormComponent form={packageTypeForm} onFinish={onFinish} />
      </CustomModal>
      <div className="w-full flex flex-col gap-3">
        <div className="flex justify-end w-[98%] ">
          <SearchFilterComponent
            advanceFilter={false}
            colDefs={packageTypeColDefs}
            searchedValues={searchHandler}
          />
        </div>
        <div className="flex justify-center h-[50vh]">
          <div className="w-[98%] ">
            <CustomAgGrid
              gridRef={gridRef}
              rowData={packageTypeInfo}
              loading={loading}
              columnDefs={packageTypeColDefs}
              isContextMenu
              contextMenuItem={[]}
              onContextMenu={() => {}}
              pagination
              className="!h-full"
              onGridReady={() => {
                loadPackageTypeInfo();
              }}
              gridName={GridNames.packageTypeGrid}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default SettingsPackageTypeComponent;
