import React, {
  ComponentType,
  SVGProps,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { IContextMenuItems, onContextMenuItemClickParams } from '@customTypes/ContextMenuTypes';
import { CellContextMenuEvent, ICellRendererParams } from 'ag-grid-community';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import {
  AssignToOutlined,
  DeleteIcon,
  EyeIcon,
  infoCircleOutlined,
  NotificationBellIcon,
  UnAssignOutlined,
  UpdateStatusIcon,
} from '@/assets';
import { GridNames } from '@/types/AppEvents';
import { IColDef } from '@/types/AgGridTypes';
import { Button, Divider, Form, Select } from 'antd';
import ColumnManage from '@/components/specific/columnManage';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import Icon from '@ant-design/icons';

import {
  advanceFilterObjectMapper,
  highlightText,
  maskQuickFilterData,
} from '@/lib/SearchFilterTypeManage';
import CustomModal from '@/components/common/modal/CustomModal';
import { useLanguage } from '@/hooks/useLanguage';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { AgGridReact } from 'ag-grid-react';
import { on } from '@/contexts/PulseContext';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { defaultPagination } from '@/constant/generalConstant';
import { orderServiceHook } from '@/api/orders/useOrders';
import {
  IOrderPaginatedResponse,
  IResponseOrderDto,
  IUpdateOrderStatusPayload,
} from '@/api/orders/order.types';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import QuickFilter from '@/components/specific/quickFilter/QuickFilter';
import { useGetDrivers } from '@/api/driver/driver.service';
import { IDrivers } from '@/api/driver/driver.types';
import { orderManagementHooks } from '@/api/orderManagment/useOrderManagement';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { OrderStatusEnums } from '@/types/enums/orderStatus';
import useThrottle from '@/hooks/useThrottle';
import LockOrderIcon from '@/assets/icons/lockOrderIcon';

interface IOrders {
  trackingNumber: string;
  status: string;
  assignee: string;
  collectionCompanyName: string;
  collectionTime: string;
  customer: string;
  dateSubmitted: string;
  deliveryCompanyName: string;
  deliveryTime: string;
  serviceLevel: string;
}
interface IAssignedFilters {
  field: string;
  operator: string;
  value: string;
  label: string;
}

const OrdersPage: React.FC = () => {
  const { t } = useLanguage();
  const [rowData, setRowData] = useState<IOrderPaginatedResponse[]>();
  const [searchText, setSearchText] = useState('');
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [isModalOpenToAssign, setIsModalOpenToAssign] = useState<{
    isOpen: boolean;
    orderId: null | string;
    driverId?: null | string;
  }>({ isOpen: false, orderId: null, driverId: null });
  const navigate = useNavigate();

  const {
    data: order,
    isLoading,
    isFetching,
    refetch: refetchOrders,
  } = orderServiceHook.useList(filterParams, { staleTime: 30000, retry: 1 });

  const { data: drivers } = useGetDrivers();
  const assignOrderMutation = orderManagementHooks.useAssignOrderToDriver();
  const unassignOrderMutation = orderManagementHooks.useUnassignOrderToDriver();
  const notificationManager = useNotificationManager();

  useEffect(() => {
    if (order) {
      setRowData(order.data);
    }
  }, [order]);

  const gridRef = useRef<AgGridReact<IOrders>>(null);

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.order.sortable.includes(field);
  }, []);

  const deleteOrderMutation = orderServiceHook.useDelete({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: 'Order successfully deleted',
      });
      await refetchOrders();
    },
  });

  const updateOrderMutation = orderServiceHook.usePatch<IUpdateOrderStatusPayload>({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: 'Order status successfully update',
      });
      await refetchOrders();
    },
  });

  const deleteOrderHandler = useThrottle(async (orderId: string) => {
    try {
      await deleteOrderMutation.mutateAsync(orderId);
      customAlert.destroy();
    } catch {
      notificationManager.error({
        message: t('common.error'),
        description: 'Failed to delete order',
      });
    }
  }, 3000);

  const updateOrderStatusHandler = useThrottle(
    async (orderId: string, status: OrderStatusEnums) => {
      try {
        if (!orderId) {
          notificationManager.success({
            message: t('common.error'),
            description: 'Order not found please try again',
          });
        }
        await updateOrderMutation.mutateAsync({
          id: `${orderId}/status`,
          data: {
            status,
            reason: 'N/A',
            comments: 'Updated from context menu',
          },
        });
        customAlert.destroy();
      } catch {
        // No need to show error message as it is handled by the api
      }
    },
    3000
  );

  const showStatusLabels: Partial<Record<OrderStatusEnums, string>> = useMemo(() => {
    return {
      [OrderStatusEnums.SUBMITTED]: 'Submitted',
      [OrderStatusEnums.IN_TRANSIT]: 'In Transit',
      [OrderStatusEnums.COMPLETED]: 'Delivered',
    };
  }, []);

  const deleteOrderConfirmation = useCallback(
    (orderId: string) => {
      customAlert.error({
        title: 'Are you sure you want to delete this order?',
        message: 'Deleting this order will remove all their details and history from your records.',
        secondButtonFunction: () => customAlert.destroy(),
        firstButtonFunction: () => deleteOrderHandler(orderId),
        firstButtonTitle: t('common.delete'),
        secondButtonTitle: t('common.cancel'),
      });
    },
    [deleteOrderHandler, t]
  );

  const updateOrderStatusConfirmation = useCallback(
    (orderId: string, status: OrderStatusEnums) => {
      const statusLabel = showStatusLabels[status] || status;
      customAlert.warning({
        title: 'Update Order Status',
        message: `Are you sure you want to change the order status to ${statusLabel}?`,
        secondButtonFunction: () => customAlert.destroy(),
        firstButtonFunction: () => updateOrderStatusHandler(orderId, status),
        firstButtonTitle: 'Confirm',
        secondButtonTitle: t('common.cancel'),
      });
    },
    [showStatusLabels, t, updateOrderStatusHandler]
  );

  const ordersColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'trackingNumber',
        headerName: t('ordersPage.trackingNumber'),
        sortable: isColumnSortable('trackingNumber'),
        unSortIcon: isColumnSortable('trackingNumber'),
        type: 'string',
        visible: true,
        cellRenderer: (params: ICellRendererParams<IResponseOrderDto>) => {
          const value = searchText ? highlightText(params.value, searchText) : params.value;
          return (
            <div className="flex gap-1 items-center">
              <div className="w-[17px]">
                {params?.data?.isLocked && <Icon component={LockOrderIcon} />}
              </div>
              {value}
            </div>
          );
        },
      },
      {
        field: 'createdAt',
        headerName: t('ordersPage.dateSubmitted'),
        visible: true,
        sortable: isColumnSortable('createdAt'),
        unSortIcon: isColumnSortable('createdAt'),
        type: 'date',
        cellRenderer: (params: ICellRendererParams) => {
          const value = params.value ? dateFormatter(params.value) : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'customerName',
        headerName: t('sidebar.customer'),
        sortable: isColumnSortable('customerName'),
        unSortIcon: isColumnSortable('customerName'),
        type: 'string',
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'collectionCompanyName',
        headerName: 'Collection Company Name',
        sortable: isColumnSortable('collectionCompanyName'),
        unSortIcon: isColumnSortable('collectionCompanyName'),
        visible: true,
        type: 'string',
        minWidth: 300,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'deliveryCompanyName',
        headerName: 'Delivery Company Name',
        sortable: isColumnSortable('deliveryCompanyName'),
        unSortIcon: isColumnSortable('deliveryCompanyName'),
        visible: true,
        type: 'string',
        minWidth: 300,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'serviceLevel',
        headerName: t('dashboard.customer.services.colDefs.serviceLevel'),
        visible: true,
        sortable: isColumnSortable('serviceLevel'),
        unSortIcon: isColumnSortable('serviceLevel'),
        type: 'string',
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'assignedDriverName',
        headerName: t('ordersPage.driver'),
        sortable: isColumnSortable('assignedDriver'),
        unSortIcon: isColumnSortable('assignedDriver'),
        minWidth: 200,
        flex: 1,
        visible: true,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return params.value || <span className="error-chip">{'Unassigned'}</span>;
        },
      },
      {
        field: 'status',
        headerName: 'Status',
        sortable: isColumnSortable('status'),
        unSortIcon: isColumnSortable('status'),
        visible: true,
        minWidth: 200,
        flex: 1,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          switch (params.value) {
            case OrderStatusEnums.DRAFT:
              return (
                <span className="grey-chip block w-full">{t('ordersPage.statusValues.draft')}</span>
              );
            case OrderStatusEnums.SUBMITTED:
              return <span className="primary-chip block w-full">{'Submitted'}</span>;
            case OrderStatusEnums.CANCELLED:
              return (
                <span className="error-chip block w-full">
                  {t('ordersPage.statusValues.cancelled')}
                </span>
              );
            case OrderStatusEnums.IN_PROGRESS:
              return (
                <span className="warning-chip block w-full">
                  {t('ordersPage.statusValues.inTransit')}
                </span>
              );
            case OrderStatusEnums.PENDING:
              return (
                <span className="warning-chip block w-full">
                  {t('ordersPage.statusValues.pending')}
                </span>
              );
            case OrderStatusEnums.ASSIGNED:
              return (
                <span className="primary-chip block w-full">
                  {t('ordersPage.statusValues.assigned')}
                </span>
              );
            case OrderStatusEnums.DELIVERED:
              return (
                <span className="success-chip block w-full">
                  {t('ordersPage.statusValues.completed')}
                </span>
              );
            default:
              break;
          }
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'scheduledCollectionTime',
        headerName: t('ordersPage.collectionTime'),
        sortable: isColumnSortable('collectionTime'),
        unSortIcon: isColumnSortable('collectionTime'),
        type: 'date',
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          const value = params.value ? dateFormatter(params.value) : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'scheduledDeliveryTime',
        headerName: t('ordersPage.deliveryTime'),
        visible: true,
        sortable: isColumnSortable('scheduledDeliveryTime'),
        unSortIcon: isColumnSortable('scheduledDeliveryTime'),
        type: 'date',
        cellRenderer: (params: ICellRendererParams) => {
          const value = params.value ? dateFormatter(params.value) : '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },

      {
        field: 'actions',
        headerName: t('zonePage.colDefs.action'),
        width: 90,
        unSortIcon: false,
        sortable: false,
        visible: true,
        pinned: 'right',
        cellRenderer: (params: ICellRendererParams) => {
          return (
            <div className="flex gap-2 h-full items-center w-full overflow-hidden">
              <Icon
                component={EyeIcon as ComponentType<SVGProps<SVGSVGElement>>}
                className="cursor-pointer"
                onClick={() => {
                  navigate(
                    ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(
                      ':id',
                      params.data.trackingNumber
                    ).replace(':tab', 'general')
                  );
                }}
              />
              <Icon
                component={DeleteIcon}
                className="cursor-pointer"
                onClick={() => deleteOrderConfirmation(params.data.id as string)}
              />
            </div>
          );
        },
      },
    ];
  }, [deleteOrderConfirmation, isColumnSortable, navigate, searchText, t]);

  const onContextMenu = useCallback((_params: CellContextMenuEvent) => {}, []);
  const searchHandler = useCallback((value: string) => {
    setSearchText(value);

    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.orderGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const clearAllToDefault = useCallback(() => {
    setSearchText('');
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearQuickFilterFunctionRef.current.handleClearAll();
  }, [
    filterParams.pageNumber,
    filterParams.pageSize,
    filterParams.searchTerm,
    filterParams.sortDirection,
  ]);

  const clearQuickFilterFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const driversOptions =
    useMemo(() => {
      return drivers?.map((driver: IDrivers) => ({ value: driver.id, label: driver.name }));
    }, [drivers]) || [];

  const assignDriverHandler = async (values: { driverId: string }) => {
    if (!isModalOpenToAssign.orderId) {
      notificationManager.error({
        message: t('common.error'),
        description: 'Order not found please try again',
      });
      return;
    }

    if (values.driverId) {
      await assignOrderMutation.mutateAsync({
        orderId: isModalOpenToAssign.orderId,
        driverId: values.driverId,
      });

      await refetchOrders();

      setIsModalOpenToAssign({ isOpen: false, orderId: null });
    }
  };

  const unAssignDriverHandler = useThrottle(async (orderId: string) => {
    if (!orderId) {
      notificationManager.error({
        message: t('common.error'),
        description: 'Order not found please try again',
      });
      return;
    }
    await unassignOrderMutation.mutateAsync(orderId);
    await refetchOrders();
    setIsModalOpenToAssign({ isOpen: false, orderId: null });
  }, 3000);

  const ordersContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: t('contextMenuItems.customer.open'),
        key: 'Open',
        onClick: ({ rowData }) => {
          navigate(
            `${ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':id', rowData.trackingNumber).replace(':tab', 'general')}`
          );
        },
        icon: EyeIcon,
      },
      {
        label: t('contextMenuItems.customer.assignTo'),
        icon: AssignToOutlined,
        key: 'assignTo',
        disabled: (params: CellContextMenuEvent) => {
          return params?.data?.isLocked;
        },
        onClick: async ({ rowData }) => {
          setIsModalOpenToAssign({
            isOpen: true,
            orderId: rowData.id,
            driverId: rowData.assignedDriverId,
          });
        },
      },
      {
        label: t('ordersPage.unassign'),
        icon: UnAssignOutlined,
        key: 'unAssign',
        disabled: (params: CellContextMenuEvent) => {
          return !params?.data?.assignedDriverName;
        },
        onClick: async (params: onContextMenuItemClickParams) => {
          await unAssignDriverHandler(params.rowData.id);
          params.closeContextMenu();
        },
      },
      {
        label: t('ordersPage.updateStatus'),
        icon: UpdateStatusIcon,
        key: 'updateStatus',
        disabled: (params: CellContextMenuEvent) => {
          return params?.data?.isLocked;
        },
        subMenu: [
          {
            label: 'Set to Submitted',
            key: 'SetToInSubmitted',
            onClick: ({ rowData }) =>
              updateOrderStatusConfirmation(rowData.id, OrderStatusEnums.SUBMITTED),
          },
          {
            label: t('ordersPage.setToInTransit'),
            key: 'SetToInTransit',
            onClick: ({ rowData }) =>
              updateOrderStatusConfirmation(rowData.id, OrderStatusEnums.IN_TRANSIT),
          },
          {
            label: t('ordersPage.setToDelivered'),
            key: 'SetToInDelivered',
            onClick: ({ rowData }) =>
              updateOrderStatusConfirmation(rowData.id, OrderStatusEnums.COMPLETED),
          },
        ],
      },
      {
        label: t('ordersPage.sendNotification'),
        icon: NotificationBellIcon,
        key: 'SendNotification',
        subMenu: [
          {
            label: t('ordersPage.sendStatusToCustomer'),
            key: 'SendStatusToCustomer',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
          {
            label: t('ordersPage.sendStatusToReceiver'),
            key: 'SendStatusToReceiver',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
          {
            label: t('ordersPage.sendStatusToCollector'),
            key: 'SendStatusToCollector',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
          {
            label: t('ordersPage.sendStatusToDriver'),
            key: 'SendStatusToDriver',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
        ],
      },
      {
        label: <span className="text-red-500">{t('common.delete')}</span>,
        icon: DeleteIcon,
        onClick: ({ rowData }) => deleteOrderConfirmation(rowData.id as string),
        key: 'delete',
      },
    ];
  }, [deleteOrderConfirmation, navigate, t, unAssignDriverHandler]);

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button
          onClick={() => {
            setIsModalOpenToAssign({ isOpen: false, orderId: null });
          }}
          className="custom-antd-outlined"
        >
          {t('common.cancel')}
        </Button>
        <Button
          type="primary"
          htmlType="submit"
          form="assignDriverForm"
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
          loading={assignOrderMutation.isPending}
        >
          {t('ordersPage.assign')}
        </Button>
      </footer>
    ),
    [assignOrderMutation.isPending, t]
  );

  return (
    <>
      <CustomModal
        className="!w-[450px]"
        modalTitle={t('contextMenuItems.customer.assignTo')}
        modalDescription={t('ordersPage.selectDriverToAssign')}
        footer={Footer}
        open={isModalOpenToAssign.isOpen}
        maskClosable={false}
        onCancel={() => {
          setIsModalOpenToAssign({ isOpen: false, orderId: null });
        }}
      >
        <div className="flex flex-col w-full gap-2">
          <div className="flex gap-2 items-center">
            <span className="font-semibold">{t('ordersPage.driver')}</span>
            <CustomTooltip>
              <img src={infoCircleOutlined} />
            </CustomTooltip>
          </div>
          <Form name="assignDriverForm" onFinish={assignDriverHandler}>
            <Form.Item name={'driverId'} initialValue={isModalOpenToAssign.driverId}>
              <Select
                showSearch
                placeholder={t('ordersPage.selectDriver')}
                className="h-[40px]"
                options={driversOptions}
                filterOption={(input, option) =>
                  String(option?.label ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase()) && option?.value !== isModalOpenToAssign.driverId
                }
              />
            </Form.Item>
          </Form>
        </div>
      </CustomModal>
      <div className="flex h-screen">
        <div className="flex-1 flex flex-col overflow-hidden bg-white">
          <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
            <div className="md:w-1/3 flex flex-col 3xsm:w-full">
              <PageHeadingComponent title={t('sidebar.orders')} />
            </div>
            <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
              <div className="flex gap-3">
                <SearchFilterComponent
                  colDefs={ordersColDefs}
                  isSetQuickFilter
                  searchInputPlaceholder={t('ordersPage.searchOrders')}
                  onSearch={searchHandler}
                  setQuickFilters={() => {}}
                  onFilterApply={applyFilters}
                  setSelectedQuickFilterData={setSelectedQuickFilterData}
                  supportedFields={filterableModules.order.advanceFilter}
                  setFilterParams={setFilterParams}
                  quickFilterEventKey={'OrderQuickFilter'}
                  quickFilterSettingsKey={'OrderQuickFilter'}
                  quickFilterTitleEventKey={'OrderQuickFilterTitleEvent'}
                  clearAllFunctionRef={clearQuickFilterFunctionRef}
                />

                <ColumnManage colDefs={ordersColDefs} gridName={GridNames.orderGrid} />
              </div>
              <div className="pt-5">
                <Divider type="vertical" className="hidden md:flex h-[40px] !m-0" />
              </div>
              <div className="pt-0 md:pt-5">
                <QuickFilter
                  eventKey={'OrderQuickFilter'}
                  quickFilterTitleEventKey={'OrderQuickFilterTitleEvent'}
                  clearAllToDefault={clearAllToDefault}
                  setFilterParams={setFilterParams}
                />
              </div>
            </div>
          </div>
          <main className=" h-screen overflow-x-hidden overflow-y-auto bg-white">
            <ActiveFilters
              selectedQuickFilterData={selectedQuickFilterData}
              clearAllToDefault={clearAllToDefault}
              colDefs={ordersColDefs}
            />
            <div className="mx-auto pr-6 py-5 flex justify-center items-center">
              <CustomAgGrid
                columnDefs={ordersColDefs}
                rowData={rowData}
                className={`${selectedQuickFilterData.length > 0 ? 'lg:!h-[85.5vh]' : 'lg:!h-[90vh] '}  `}
                loading={isLoading || isFetching}
                pagination
                tooltipMouseTrack
                isContextMenu={true}
                contextMenuItem={ordersContextMenuItems}
                onContextMenu={onContextMenu}
                gridName={GridNames.orderGrid}
                emptyState={{
                  title: t('ordersPage.noOrdersFound'),
                  description: '',
                }}
                gridId="gridWrapperForChildren"
                gridRef={gridRef}
              />
            </div>
          </main>
        </div>
      </div>
    </>
  );
};

export default OrdersPage;
