import React, { useMemo } from 'react';
import { DatePicker, Form, Input, Select } from 'antd';
import CustomDivider from '@/components/common/divider/CustomDivider';
import { numberFieldValidator } from '@/lib/FormValidators';
import { calculateTime } from '@/lib/helper';
import { IVehicleTimeClockSessionsOperationFormProps } from '../../../vehicleTypes';
import { useConfig } from '@/contexts/ConfigContext';
import { useLanguage } from '@/hooks/useLanguage';
import { WarningOutlined } from '@ant-design/icons';
import SelectDownArrow from '@/assets/icons/selectDownArrow';
import dayjs, { Dayjs } from 'dayjs';
import { RuleObject } from 'antd/es/form';
import { formErrorRegex } from '@/constant/Regex';
import { useGetDrivers } from '@/api/driver/driver.service';
import { IDrivers } from '@/api/driver/driver.types';

const { RangePicker } = DatePicker;

const VehicleTimeClockSessionsOperationForm: React.FC<
  IVehicleTimeClockSessionsOperationFormProps
> = (props): JSX.Element => {
  const { form, onFinish } = props;
  const { config } = useConfig();
  const { t } = useLanguage();
  const { data: drivers } = useGetDrivers();

  const driversOptions =
    useMemo(() => {
      return drivers?.map((driver: IDrivers) => ({ value: driver.id, label: driver.name }));
    }, [drivers]) || [];

  const handleValuesChange = (dateTime: any) => {
    if (!dateTime) {
      return form.setFieldsValue({ totalTime: '0h 0min' });
    }

    if (dateTime?.length > 1) {
      const totalMinutes: string = calculateTime(dateTime[0], dateTime[1])?.totalMinutes || '0';
      form.setFieldsValue({ totalTime: totalMinutes });
      form.setFieldsValue({ showTotalTimeWarning: false });
    }
  };

  const dateAndTimeValidator = (_: RuleObject, values: [Dayjs, Dayjs]) => {
    if (dayjs(values[0]).isSame(dayjs(values[1]))) {
      return Promise.reject(t('vehiclePage.form.sameTimeError'));
    }
    if (form.getFieldValue('showTotalTimeWarning')) {
      return Promise.reject(t('vehiclePage.messages.requireEndDate'));
    }
    return Promise.resolve();
  };

  const maxDistanceValidator = (_: RuleObject, value: string) => {
    if (Number(value) < 1) {
      return Promise.reject(t('vehiclePage.minDistance'));
    }
    return Promise.resolve();
  };

  return (
    <Form
      className="custom-form"
      name="time-clock-sessions"
      layout="vertical"
      form={form}
      preserve={false}
      onValuesChange={handleValuesChange}
      onFinish={onFinish}
    >
      <div className="form-fields-wrapper flex gap-2.5 flex-col">
        <CustomDivider label={t('common.divider.basicDetails')} />
        <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 ">
          <Form.Item
            label={t('vehiclePage.timeClockSessionForm.driverName.label')}
            name="driverId"
            rules={[
              {
                required: true,
                message: t('vehiclePage.timeClockSessionForm.driverName.errorMessage'),
              },
            ]}
          >
            <Select
              options={driversOptions}
              suffixIcon={<SelectDownArrow />}
              placeholder={t('vehiclePage.timeClockSessionForm.driverName.placeholder')}
              prefixCls="custom-select"
            />
          </Form.Item>
          <Form.Item
            validateFirst
            label={
              <>
                <span className="text-primary-200 font-normal">({config.units?.distance})</span>
                {t('vehiclePage.timeClockSessionForm.distance.label')}
              </>
            }
            name="distanceTraveled"
            rules={[
              {
                required: true,
                message: t('vehiclePage.timeClockSessionForm.distance.errorMessage'),
              },
              {
                validator: maxDistanceValidator,
              },
              {
                pattern: formErrorRegex.DIGITS_WITH_DECIMAL,
                message: t('vehiclePage.messages.invalidDecimal'),
              },
            ]}
          >
            <Input
              placeholder={`24${config.units?.distance}`}
              maxLength={8}
              onKeyDown={(event) => numberFieldValidator(event, { allowDecimals: true })}
            />
          </Form.Item>
        </div>
        <Form.Item
          label={t('vehiclePage.timeClockSessionForm.dateTime.label')}
          name="dateTime"
          validateFirst
          rules={[
            {
              required: true,
              message: t('vehiclePage.timeClockSessionForm.dateTime.errorMessage'),
            },
            {
              validator: dateAndTimeValidator,
            },
          ]}
        >
          <RangePicker
            prefixCls="custom-date-picker"
            showTime
            showSecond={false}
            use12Hours={config.is12HoursFormate}
            format={config.dateFormate}
            onChange={handleValuesChange}
            placeholder={[t('vehiclePage.startTime'), t('vehiclePage.endTime')]}
          />
        </Form.Item>
        <Form.Item name={'showTotalTimeWarning'} noStyle hidden />
        <Form.Item shouldUpdate>
          {() => (
            <Form.Item name={'totalTime'}>
              {form.getFieldValue('showTotalTimeWarning') ? (
                <div className="flex gap-2 bg-warning-50 w-full rounded p-2 font-semibold mt-2 text-warning-600 select-none">
                  <WarningOutlined />
                  {t('vehiclePage.totalTimeWarning')}
                </div>
              ) : (
                <div className="bg-primary-25 w-full rounded p-2 font-semibold mt-2 text-primary-500">
                  {t('vehiclePage.totalTime')} :{' '}
                  <span>{form.getFieldValue('totalTime') || '0h 0min'}</span>
                </div>
              )}
            </Form.Item>
          )}
        </Form.Item>
      </div>
    </Form>
  );
};

export default VehicleTimeClockSessionsOperationForm;
