import CustomDivider from '@/components/common/divider/CustomDivider';
import { useLanguage } from '@/hooks/useLanguage';
import {
  numberFieldValidator,
  validateCountryAndValue,
  validateMaskedInput,
} from '@/lib/FormValidators';
import { Form, Input, InputNumber, InputRef, Select, Space } from 'antd';
import MaskedInput from 'antd-mask-input';
import { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import TextArea from 'antd/es/input/TextArea';
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { IAddressOperationFormProps } from '../address.type';
import { formErrorRegex } from '@/constant/Regex';
import usePreventExits from '@/hooks/usePreventExits';
import { isFormChangedHandler } from '@/lib/helper';
import { Autocomplete } from '@react-google-maps/api';
import { googlePlaceDataMasking } from '@/lib/GooglePlace';
import { useGooglePlaceDropdownFix } from '@/hooks/useGooglePlaceDropdownFix';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { infoCircleOutlined } from '@/assets';
import { zoneService } from '@/api/zones/useZones';
import { TrackedError } from '@/types/AxiosTypes';
import { AxiosError } from 'axios';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { customerHook } from '@/api/customer/useCustomer';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';

const AddressOperationForm: React.FC<IAddressOperationFormProps> = (props) => {
  const { form, onFinish, open } = props;
  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);

  const [searchResult, setSearchResult] = useState<google.maps.places.Autocomplete | null>();
  const inputPhoneRef = useRef<InputRef>(null);
  const { t } = useLanguage();
  const { data: customerList } = customerHook.useEntities('all/minimal');
  const InitialValue = useMemo(() => (open.isEdit ? form.getFieldsValue(true) : {}), [open.isEdit]);
  const autocompleteRef = useRef<Autocomplete>(null);

  const customerOptions = useMemo(() => {
    return customerList?.data.map((customer) => ({
      value: customer.id,
      label: `${customer.companyName} - ${customer.contactName} `,
    }));
  }, [customerList?.data]);

  const maskingInputPhone = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    setMaskPhoneInput(selectedCountryMask);
    if (focus) {
      inputPhoneRef?.current?.focus();
    }
  }, []);

  useEffect(() => {
    maskingInputPhone(form.getFieldValue('countryCode'), false);
  }, [form, maskPhoneInput, maskingInputPhone]);

  const PhoneNumberAddonBefore = useMemo(
    () => (
      <Form.Item name="countryCode" noStyle initialValue={optionsForPrefix[0].value}>
        <Select
          options={optionsForPrefix}
          placeholder="USA +1"
          onChange={(value) => maskingInputPhone(value)}
        />
      </Form.Item>
    ),
    [maskingInputPhone]
  );

  const { setPreventExit } = usePreventExits();
  const { setIsBlocked } = useNavigationContext();

  useGooglePlaceDropdownFix(open.isOpen, 'autoComplete');

  const onLoad = useCallback((autocomplete: google.maps.places.Autocomplete | null | undefined) => {
    setSearchResult(autocomplete);
  }, []);

  const onPlaceChanged = useCallback(async () => {
    if (searchResult != null) {
      const place = searchResult.getPlace();
      const selectedPlaceData = googlePlaceDataMasking(place);

      const newFormValues = {
        addressLine1: selectedPlaceData?.addressLine1,
        city: selectedPlaceData?.city,
        postalCode: selectedPlaceData?.postalCode,
        province: selectedPlaceData?.state,
        country: selectedPlaceData?.country,
        latitude: selectedPlaceData?.latitude,
        longitude: selectedPlaceData?.longitude,
      };
      form.setFieldsValue(newFormValues);
      try {
        form.resetFields(['zone']);
        if (selectedPlaceData?.postalCode) {
          const trimmedPostalCode = selectedPlaceData?.postalCode.split(' ')[0];
          const response = await zoneService.getById(`postalCode/${trimmedPostalCode}`);
          form.setFieldValue('zone', response.name);
          await form.validateFields(['zone']);
        } else {
          form.setFields([
            {
              name: 'zone',
              errors: [t('addressPage.operationalForm.noPostalCodeFound')],
            },
          ]);
        }
      } catch (error: unknown) {
        if (error instanceof AxiosError) {
          const errorStack = error?.response?.data as TrackedError;
          if (errorStack?.code === 406007) {
            form.resetFields(['zone']);
            form.setFields([
              {
                name: 'zone',
                errors: [
                  t('addressPage.operationalForm.zoneError', {
                    code: errorStack?.details?.postalCode.split(' ')[0],
                  }),
                ],
              },
            ]);
          }
        }
      }
    }
  }, [form, searchResult, t]);

  const postalCodeWatcherValue = Form.useWatch('postalCode', form);

  const onAutocompleteChangeHandler = () => {
    const val = form.getFieldsValue(['province', 'city', 'country', 'postalCode']);
    if (val.province || val.city || val.country || val.postalCode) {
      form.resetFields(['province', 'city', 'country', 'postalCode', 'zone']);
    }
  };

  return (
    <Form
      name="address-form"
      layout="vertical"
      className="custom-form"
      form={form}
      onFinish={onFinish}
      onFinishFailed={(errors) => {
        form.scrollToField(errors.errorFields[0].name, { behavior: 'smooth' });
      }}
      onFieldsChange={(changesFields) => {
        if (changesFields.length <= 1) {
          const isIsChange = isFormChangedHandler(InitialValue, form.getFieldsValue(true), [
            'zone',
            'countryCode',
          ]);

          setPreventExit(isIsChange);
        } else if (changesFields.length > 1) {
          setIsBlocked(false);
          setPreventExit(false);
        }
      }}
      preserve={false}
    >
      <CustomDivider label={t('common.divider.basicDetails')} />
      <div className="form-fields-wrapper flex gap-2.5 flex-col">
        <Form.Item
          label={t('addressPage.operationalForm.customer')}
          name="customerId"
          rules={[{ required: true, message: t('addressPage.operationalForm.customerError') }]}
        >
          <Select
            options={customerOptions}
            placeholder={t('addressPage.operationalForm.customerPlaceholder')}
            prefixCls="custom-select"
            disabled={open.isEdit}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>
        <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
          <Form.Item
            label={t('addressPage.operationalForm.name')}
            validateFirst
            name="name"
            rules={[
              { required: true, message: t('addressPage.operationalForm.nameError') },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
            ]}
          >
            <Input placeholder={t('addressPage.operationalForm.namePlaceholder')} maxLength={255} />
          </Form.Item>
          <Form.Item
            label={t('addressPage.operationalForm.companyName')}
            name="companyName"
            validateFirst
            rules={[
              {
                required: true,
                message: t('addressPage.operationalForm.companyNameError'),
              },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
            ]}
          >
            <Input
              placeholder={t('addressPage.operationalForm.companyNamePlaceholder')}
              maxLength={255}
            />
          </Form.Item>
        </div>

        <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
          <Form.Item
            validateFirst
            label={t('addressPage.operationalForm.email')}
            name="email"
            rules={[
              {
                required: true,
                message: t('addressPage.operationalForm.emailError'),
              },
              { type: 'email', message: t('addressPage.operationalForm.emailTypeError') },
            ]}
          >
            <Input placeholder={t('addressPage.operationalForm.emailPlaceholder')} maxLength={70} />
          </Form.Item>

          <Space.Compact className="combined-input">
            <Form.Item
              dependencies={['countryCode']}
              validateFirst
              className="w-[75%]"
              rules={[
                {
                  required: true,
                  validator: validateCountryAndValue(form, 'countryCode', 'phone number', true),
                },
                {
                  validator: (_, value) =>
                    validateMaskedInput(
                      value,
                      maskPhoneInput.length,
                      t('addressPage.operationalForm.validPhoneNumberError')
                    ),
                },
              ]}
              name="phoneNumber"
              label={t('addressPage.operationalForm.phoneNumber')}
            >
              <MaskedInput
                ref={inputPhoneRef}
                addonBefore={PhoneNumberAddonBefore}
                className="customer-general-maskedInput address-popup-maskedInput"
                placeholder={t('addressPage.operationalForm.phoneNumberPlaceholder')}
                mask={maskPhoneInput.mask}
                onChange={(event) => form.setFieldValue('phoneNumber', event?.unmaskedValue)}
              />
            </Form.Item>
            <Form.Item name="phoneExtension" className="w-[25%]">
              <Input
                placeholder={t('addressPage.operationalForm.phoneExtPlaceholder')}
                maxLength={6}
                onKeyDown={(event) => numberFieldValidator(event, {})}
              />
            </Form.Item>
          </Space.Compact>
        </div>
        <CustomDivider label={t('addressPage.operationalForm.locationDividerText')} />
        <Autocomplete onLoad={onLoad} onPlaceChanged={onPlaceChanged} ref={autocompleteRef}>
          <Form.Item
            label={t('addressPage.operationalForm.addressLine1')}
            rules={[
              {
                required: true,
                message: t('addressPage.operationalForm.addressLine1Error'),
              },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
            ]}
            name="addressLine1"
          >
            <Input
              placeholder={t('addressPage.operationalForm.addressLine1Placeholder')}
              maxLength={255}
              id="autoComplete"
              onChange={onAutocompleteChangeHandler}
            />
          </Form.Item>
        </Autocomplete>
        <Form.Item name="latitude" hidden preserve>
          <InputNumber />
        </Form.Item>
        <Form.Item name="longitude" hidden preserve>
          <InputNumber />
        </Form.Item>

        <Form.Item
          label={t('addressPage.operationalForm.addressLine2')}
          name="addressLine2"
          rules={[
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
          ]}
        >
          <Input
            placeholder={t('addressPage.operationalForm.addressLine2Placeholder')}
            maxLength={255}
          />
        </Form.Item>
        <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
          <Form.Item
            label={t('addressPage.operationalForm.city')}
            rules={[{ required: true, message: t('addressPage.operationalForm.cityError') }]}
            name="city"
          >
            <Input
              placeholder={t('addressPage.operationalForm.cityPlaceholder')}
              maxLength={255}
              disabled
            />
          </Form.Item>

          <Form.Item
            label={t('addressPage.operationalForm.province')}
            rules={[{ required: true, message: t('addressPage.operationalForm.provinceError') }]}
            name="province"
          >
            <Input
              placeholder={t('addressPage.operationalForm.provincePlaceholder')}
              maxLength={100}
              disabled
            />
          </Form.Item>
          <Form.Item
            label={t('addressPage.operationalForm.postalCode')}
            validateFirst
            rules={[
              { required: true, message: t('addressPage.operationalForm.postalCodeError') },
              {
                pattern: formErrorRegex.PostalCode,
                message: t('addressPage.operationalForm.validPostalCodeError'),
              },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
            ]}
            name="postalCode"
          >
            <Input
              placeholder={t('addressPage.operationalForm.postalCodePlaceholder')}
              maxLength={20}
              disabled
            />
          </Form.Item>
          <Form.Item
            label={t('addressPage.operationalForm.country')}
            rules={[{ required: true, message: t('addressPage.operationalForm.countryError') }]}
            name="country"
          >
            <Input
              placeholder={t('addressPage.operationalForm.countryPlaceholder')}
              maxLength={100}
              disabled
            />
          </Form.Item>
        </div>

        <Form.Item
          label={
            <span className="flex gap-1">
              {t('addressPage.operationalForm.zone')}
              <CustomTooltip title={t('customerAddressPage.operationalForm.zoneToolTip')}>
                <img src={infoCircleOutlined} alt="info" />
              </CustomTooltip>
            </span>
          }
          validateFirst
          rules={[
            {
              required: true,
              message: postalCodeWatcherValue
                ? t('addressPage.operationalForm.zoneError', {
                    code: postalCodeWatcherValue.split(' ')[0],
                  })
                : t('addressPage.operationalForm.noPostalCodeFound'),
            },
          ]}
          name="zone"
          dependencies={['addressLine1']}
        >
          <Input maxLength={50} disabled />
        </Form.Item>

        <CustomDivider label={t('addressPage.operationalForm.commentsDividerTex')} />

        <Form.Item
          label={
            <>
              <CustomTooltip title={t('customerAddressPage.operationalForm.notesTooltip')}>
                <img src={infoCircleOutlined} alt="info" />
              </CustomTooltip>
              {t('addressPage.operationalForm.comments')}
            </>
          }
          name="notes"
        >
          <TextArea
            placeholder={t('addressPage.operationalForm.commentsPlaceHolder')}
            maxLength={255}
            style={{
              minHeight: 54,
              maxHeight: 100,
            }}
          />
        </Form.Item>
      </div>
    </Form>
  );
};

export default memo(AddressOperationForm);
