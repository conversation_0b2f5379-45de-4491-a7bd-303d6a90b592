import { useCallback, useMemo } from 'react';
import { IColDef } from '@/types/AgGridTypes.ts';
import { ICellRendererParams } from 'ag-grid-community';
import { IZone, ZoneProps } from '@pages/location/zone/list/zone.type.ts';
import { DeleteIcon, EyeIcon, HistoryIcon } from '@/assets';
import Icon from '@ant-design/icons';
import { useLanguage } from '@/hooks/useLanguage';
import { Tag } from 'antd';

function escapeRegex(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function useHighlightText() {
  return useCallback(
    (value: string | string[] | null | undefined, search: string): string | JSX.Element => {
      const text = Array.isArray(value) ? value.join(', ') : (value ?? '');

      if (!search || !text) return text;

      const escapedSearch = escapeRegex(search);
      const regex = new RegExp(`(${escapedSearch})`, 'gi');
      const parts = text.split(regex);

      return (
        <span
          dangerouslySetInnerHTML={{
            __html: parts
              .map((part) =>
                regex.test(part) ? `<span style="background-color: yellow;">${part}</span>` : part
              )
              .join(''),
          }}
        />
      );
    },
    []
  );
}

export function useZoneColDefs({
  searchText,
  onEditZone,
  onDeleteZone,
  alreadyInUsedPostalCodes,
  isColumnSortable,
}: ZoneProps) {
  const highlightText = useHighlightText();
  const { t } = useLanguage();
  const zoneColDefs: IColDef[] = useMemo(
    () => [
      {
        field: 'name',
        headerName: `${t('zonePage.colDefs.name')}`,
        sortable: isColumnSortable('name'),
        unSortIcon: isColumnSortable('name'),
        minWidth: 180,
        type: 'string',
        flex: 1,
        visible: true,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'postalCodes',
        headerName: `${t('addressPage.colDefs.postalCode')}`,
        sortable: isColumnSortable('postalCodes'),
        unSortIcon: isColumnSortable('postalCodes'),
        width: 600,
        minWidth: 300,
        type: 'string',
        visible: true,
        autoHeight: true, // Enable auto height for this column
        cellRenderer: (params: { value: string[] }) => {
          if (!params.value) return null;
          return (
            <div
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                padding: '3px',
                maxWidth: '600px',
                overflow: 'auto',
                gap: '3px',
              }}
            >
              {params.value.map((currentCode, index) => (
                <Tag
                  key={index}
                  className={
                    alreadyInUsedPostalCodes!.filter((code) => code === currentCode).length > 1
                      ? 'text-primary-600 border border-primary-100 rounded-md'
                      : ''
                  }
                >
                  {currentCode}
                </Tag>
              ))}
            </div>
          );
        },
      },
      {
        field: 'notes',
        headerName: `${t('vehiclePage.labels.comments')}`,
        minWidth: 180,
        flex: 1,
        sortable: isColumnSortable('notes'),
        unSortIcon: isColumnSortable('notes'),
        type: 'string',
        visible: true,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'action',
        headerName: `${t('zonePage.colDefs.action')}`,
        pinned: 'right',
        minWidth: 110,
        maxWidth: 110,
        visible: true,
        sortable: false,
        resizable: false,
        cellRenderer: (params: ICellRendererParams<IZone>) => (
          <div className="flex gap-2 h-full items-center">
            <Icon component={HistoryIcon} className="cursor-pointer" alt="history" />

            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view/edit"
              onClick={() => {
                if (onEditZone && params.data) {
                  onEditZone(params.data);
                }
              }}
            />
            <Icon
              component={DeleteIcon}
              className="cursor-pointer"
              alt="delete"
              onClick={() => {
                if (onDeleteZone && params.data?.id) {
                  onDeleteZone(params.data);
                }
              }}
            />
          </div>
        ),
      },
    ],
    [
      t,
      isColumnSortable,
      searchText,
      highlightText,
      alreadyInUsedPostalCodes,
      onEditZone,
      onDeleteZone,
    ]
  );

  return zoneColDefs;
}
