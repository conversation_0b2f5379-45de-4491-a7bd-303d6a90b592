.zone-select>.ant-select-selector {
  @apply !min-h-[40px] !h-fit;
}

.zone-select>.ant-select-selector>.ant-select-selection-wrap {
  height: 100%;
}

.zone-select>.ant-select-selector>.ant-select-selection-wrap>.ant-select-selection-overflow>.ant-select-selection-overflow-item>.ant-select-selection-item>.ant-select-selection-item-content {
  color: #0f4e6b;
  font-weight: 450;
}

.zone-select>.ant-select-selector>.ant-select-selection-wrap>.ant-select-selection-overflow>.ant-select-selection-overflow-item>.ant-select-selection-item {
  background: var(--primary-25);
  height: 35px;
  align-items: center;
}

.lookup-table-name>.ant-form-item-row>.ant-form-item-label>label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 2px;
  font-weight: 600;
}

.lookup-table-name>.ant-form-item-row>.ant-form-item-label>label::after {
  display: none;
}

ol li::marker {
  font-weight: 600;
}

.fixed-radio,
.percentage-radio>input[type='radio'] {
  accent-color: var(--primary-600);
}

.bulk-adjust-input>.ant-input-number-wrapper>.ant-input-number>.ant-input-number-input-wrap>input,
.bulk-adjust-input input {
  height: 40px;
}