import { URoles } from '@/types/enums/Roles';

export const ROUTES = {
  COMMON: {
    DEFAULT: '/',
    DASHBOARD: '/dashboard',
    LOGIN: '/login',
    UN_AUTHORIZED: '/unauthorized',
    NOT_EXIST: '*',
    INTERNAL_SERVER_ERROR: '/internalServerError',
    FORGET_PASSWORD: '/forgetPassword',
    OTP_VERIFICATION: '/otpVerification',
    RESET_PASSWORD: '/resetPassword',
  },
  CUSTOMER: {
    CUSTOMER_CUSTOMERS: '/customer/customersList',
    CUSTOMER_ADD: '/customer/add',
    CUSTOMER_DASHBOARD: '/customer/dashboard',
    CUSTOMER_PARTNER: '/customer/partner',
    CUSTOMER_TRACKING: '/tracking',
    CUSTOMER_BILLING: '/customer/billing',
    CUSTOMER_TAB: '/customer/edit/:id/:tab',
    CUSTOMER_EDIT: '/customer/edit/',
  },
  LOCATION: {
    LOCATION_ADDRESS: '/location/address',
    LOCATION_ZONE: '/location/zone',
    LOCATION_ZONE_LOOKUP_TABLE: '/location/lookup-table',
    LOCATION_ZONE_LOOKUP_TABLE_ADD: '/location/lookup-table/add',
    LOCATION_ZONE_LOOKUP_TABLE_EDIT: '/location/lookup-table/edit/:id',
    LOCATION_ROUTES: '/location/routes',
  },

  LOGISTIC: {
    LOGISTICS_ORDERS: '/logistic/orders',
    LOGISTICS_ORDERS_OPERATION: '/logistic/orders/edit/:id/:tab',
    LOGISTICS_DISPATCHER: '/logistic/dispatcher',
    LOGISTICS_VEHICLE: '/logistic/vehicle',
    LOGISTICS_VEHICLE_ADD: '/logistic/vehicle/add/:tab',
    LOGISTICS_VEHICLE_EDIT: '/logistic/vehicle/edit/:vehicleId/:tab',
  },
  PRICES: {
    PRICES_PRICE_SETS: '/prices/priceSets',
    PRICES_PRICE_SETS_ADD: '/prices/priceSets/add',
    PRICES_PRICE_SETS_EDIT: '/prices/priceSets/edit/:id/:tab',
    PRICES_PRICE_MODIFIERS: '/prices/priceModifiers',
    PRICES_PRICE_MODIFIER_ADD: '/prices/priceModifiers/add/:tab',
    PRICES_PRICE_MODIFIER_EDIT: '/prices/priceModifiers/edit/:id/:tab',
    PRICES_PRICE_GROUP_MODIFIER_ADD: '/prices/priceModifiers/group/add/:tab',
    PRICES_PRICE_GROUP_MODIFIER_EDIT: '/prices/priceModifiers/group /edit/:id/:tab',
  },
  SETTINGS: {
    SETTINGS_GENERAL: '/settings/general',
    SETTINGS_ACCOUNT: '/settings/accountSettings',
    SETTINGS_OPERATION: '/settings/operationSettings',
    SETTINGS_PARTNERS: '/settings/partners',
    SETTINGS_TEMPLATE: '/settings/templates',
    SETTINGS_USERS: '/settings/users',
    SETTINGS_IMPORT_EXPORT: '/settings/importExport',
  },
  BILLING: {
    BILLING_INVOICES_GRID: '/billing/invoices',
    BILLING_CREATE_INVOICE: '/billing/createInvoice',
    BILLING_EDIT_INVOICE: '/billing/editInvoice/:id',
    BILLING_PAYMENTS_GRID: '/billing/payments',
    BILLING_ADD_PAYMENT: '/billing/payments/add',
    BILLING_EDIT_PAYMENT: '/billing/payments/edit/:id',
  },
};

export const USERS_DEFAULT_ROUTE: Record<URoles, string> = {
  Tenant: ROUTES.CUSTOMER.CUSTOMER_CUSTOMERS,
  Dispatcher: ROUTES.LOGISTIC.LOGISTICS_ORDERS,
};
