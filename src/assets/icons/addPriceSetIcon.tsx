const AddPriceSetIcon = ({ bool }: { bool: boolean }) => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_9243_152400)">
        <path
          d="M9 7.0625C10.0664 7.0625 10.9375 7.93364 10.9375 9C10.9375 10.0664 10.0664 10.9375 9 10.9375C7.93364 10.9375 7.0625 10.0664 7.0625 9C7.0625 7.93364 7.93364 7.0625 9 7.0625ZM9 7.1875C8.00386 7.1875 7.1875 8.00386 7.1875 9C7.1875 9.99614 8.00386 10.8125 9 10.8125C9.99614 10.8125 10.8125 9.99614 10.8125 9C10.8125 8.00386 9.99614 7.1875 9 7.1875Z"
          stroke={bool ? '#2D3484' : '#20363F'}
        />
        <path
          d="M13.875 7.0625C13.8881 7.0625 13.9037 7.06774 13.918 7.08203C13.9323 7.09632 13.9375 7.11194 13.9375 7.125V10.875C13.9375 10.8881 13.9323 10.9037 13.918 10.918C13.9037 10.9323 13.8881 10.9375 13.875 10.9375C13.8619 10.9375 13.8463 10.9323 13.832 10.918C13.8177 10.9037 13.8125 10.8881 13.8125 10.875V7.125C13.8125 7.11194 13.8177 7.09632 13.832 7.08203C13.8463 7.06774 13.8619 7.0625 13.875 7.0625Z"
          fill={bool ? '#2D3484' : '#20363F'}
          stroke={bool ? '#2D3484' : '#20363F'}
        />
        <path
          d="M3.75 10.4375C5.43886 10.4375 6.8125 11.8111 6.8125 13.5C6.8125 13.9908 6.69448 14.4709 6.47168 14.8965L6.36914 15.0752L6.36426 15.083C5.82553 15.9935 4.82078 16.5625 3.75 16.5625C2.67959 16.5625 1.67458 15.9943 1.13574 15.0771L0.714844 15.3242L1.13672 15.0762L1.12891 15.0654L1.02734 14.8896C0.805714 14.4705 0.6875 13.9913 0.6875 13.5C0.6875 11.8111 2.06114 10.4375 3.75 10.4375ZM3.75 10.5625C2.13136 10.5625 0.8125 11.8814 0.8125 13.5C0.8125 14.0286 0.955206 14.5514 1.23438 15.0059V15.0068C1.76053 15.8959 2.72808 16.4375 3.75 16.4375C4.77036 16.4375 5.73506 15.8973 6.26172 15.0186L6.2627 15.0195C6.54267 14.5588 6.6875 14.0414 6.6875 13.5C6.6875 11.8814 5.36864 10.5625 3.75 10.5625Z"
          fill={bool ? '#2D3484' : '#20363F'}
          stroke={bool ? '#2D3484' : '#20363F'}
        />
        <path
          d="M2.63281 13.4229H4.875C4.88802 13.4229 4.90372 13.4282 4.91797 13.4424C4.93226 13.4567 4.9375 13.4723 4.9375 13.4854C4.9375 13.4984 4.93226 13.514 4.91797 13.5283C4.90372 13.5425 4.88802 13.5479 4.875 13.5479H2.63281C2.61975 13.5479 2.60413 13.5426 2.58984 13.5283C2.57555 13.514 2.57031 13.4984 2.57031 13.4854C2.57031 13.4723 2.57555 13.4567 2.58984 13.4424C2.60413 13.4281 2.61975 13.4229 2.63281 13.4229Z"
          fill={bool ? '#2D3484' : '#20363F'}
          stroke={bool ? '#2D3484' : '#20363F'}
        />
        <path
          d="M3.75 12.3271C3.76306 12.3271 3.77868 12.3324 3.79297 12.3467C3.80726 12.361 3.8125 12.3766 3.8125 12.3896V14.6318C3.8125 14.6664 3.78571 14.6943 3.75 14.6943C3.73694 14.6943 3.72132 14.6891 3.70703 14.6748C3.69286 14.6606 3.6875 14.6449 3.6875 14.6318V12.3896C3.6875 12.3766 3.69274 12.361 3.70703 12.3467C3.72132 12.3324 3.73694 12.3271 3.75 12.3271Z"
          fill={bool ? '#2D3484' : '#20363F'}
          stroke={bool ? '#2D3484' : '#20363F'}
        />
        <path
          d="M12.75 15.5625H6.375C6.0675 15.5625 5.8125 15.3075 5.8125 15C5.8125 14.6925 6.0675 14.4375 6.375 14.4375H12.75C14.895 14.4375 15.9375 13.395 15.9375 11.25V6.75C15.9375 4.605 14.895 3.5625 12.75 3.5625H5.25C3.105 3.5625 2.0625 4.605 2.0625 6.75V11.475C2.0625 11.7825 1.8075 12.0375 1.5 12.0375C1.1925 12.0375 0.9375 11.7825 0.9375 11.475V6.75C0.9375 4.0125 2.5125 2.4375 5.25 2.4375H12.75C15.4875 2.4375 17.0625 4.0125 17.0625 6.75V11.25C17.0625 13.9875 15.4875 15.5625 12.75 15.5625Z"
          fill={bool ? '#2D3484' : '#20363F'}
        />
      </g>
      <defs>
        <clipPath id="clip0_9243_152400">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default AddPriceSetIcon;
