export const UnAssignOutlined = ({ bool }: { bool: boolean }) => {
  const currentColor = bool ? '#2D3484' : '#20363F';
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1943_140299)">
        <path
          d="M9 9.0625C6.89864 9.0625 5.1875 7.35136 5.1875 5.25C5.1875 3.14864 6.89864 1.4375 9 1.4375C11.1014 1.4375 12.8125 3.14864 12.8125 5.25C12.8125 7.35136 11.1014 9.0625 9 9.0625ZM9 1.5625C6.96886 1.5625 5.3125 3.21886 5.3125 5.25C5.3125 7.28114 6.96886 8.9375 9 8.9375C11.0311 8.9375 12.6875 7.28114 12.6875 5.25C12.6875 3.21886 11.0311 1.5625 9 1.5625Z"
          stroke={currentColor}
        />
        <path
          d="M11.0858 11.4688L11.0858 11.4689L11.0886 11.4696C11.119 11.478 11.1392 11.5079 11.129 11.5449C11.1206 11.5755 11.0907 11.5956 11.0537 11.5854L10.9211 12.0675L11.0561 11.5861C10.3935 11.4002 9.70271 11.3125 9.00111 11.3125C5.59698 11.3125 2.62109 13.5363 2.62109 16.5C2.62109 16.5131 2.61589 16.5287 2.6016 16.543C2.58731 16.5573 2.57166 16.5625 2.55859 16.5625C2.54553 16.5625 2.52987 16.5573 2.51558 16.543C2.50129 16.5287 2.49609 16.5131 2.49609 16.5C2.49609 13.6568 5.32351 11.1875 9.00111 11.1875C9.71537 11.1875 10.4122 11.2794 11.0858 11.4688Z"
          fill={currentColor}
          stroke={currentColor}
        />
        <path
          d="M11.4586 15.7699L11.4533 15.765L11.4479 15.7604C11.2278 15.5717 11.0311 15.3372 10.879 15.0774L10.8791 15.0774L10.8741 15.0693C10.5927 14.6087 10.4375 14.0616 10.4375 13.5C10.4375 12.6935 10.7462 11.9403 11.3019 11.3651C11.882 10.7658 12.6594 10.4375 13.5 10.4375C14.3787 10.4375 15.2087 10.8125 15.7722 11.4528L15.7721 11.4528L15.7752 11.4563C16.2798 12.0191 16.5625 12.7408 16.5625 13.5C16.5625 13.7417 16.5306 13.9816 16.4682 14.2062L16.4647 14.2188L16.4619 14.2315C16.3992 14.5138 16.2786 14.8122 16.1152 15.0697L16.1104 15.0774L16.1058 15.0852C15.575 15.9933 14.5714 16.5625 13.5 16.5625C12.7411 16.5625 12.0205 16.2801 11.4586 15.7699ZM11.3961 11.4468L11.3961 11.4468L11.3931 11.4499C10.8616 11.9994 10.5625 12.7236 10.5625 13.5C10.5625 14.0295 10.7052 14.5534 10.9853 15.0084C11.1334 15.2653 11.3243 15.4913 11.5389 15.6799C12.0795 16.1736 12.7754 16.445 13.5 16.445C14.5208 16.445 15.4868 15.9049 16.0133 15.0255C16.1658 14.774 16.2829 14.4881 16.348 14.2052C16.4108 13.9683 16.4375 13.7399 16.4375 13.5075C16.4375 12.7835 16.1666 12.0893 15.6832 11.55C15.1343 10.9125 14.3353 10.5625 13.5 10.5625C12.6965 10.5625 11.9537 10.8802 11.3961 11.4468Z"
          fill={currentColor}
          stroke={currentColor}
        />
        <path
          d="M14.6253 13.5474H12.3828C12.3697 13.5474 12.3541 13.5422 12.3398 13.5279C12.3255 13.5136 12.3203 13.4979 12.3203 13.4849C12.3203 13.4718 12.3255 13.4561 12.3398 13.4419C12.3541 13.4276 12.3697 13.4224 12.3828 13.4224H14.6253C14.6384 13.4224 14.654 13.4276 14.6683 13.4419C14.6826 13.4561 14.6878 13.4718 14.6878 13.4849C14.6878 13.4979 14.6826 13.5136 14.6683 13.5279C14.654 13.5422 14.6384 13.5474 14.6253 13.5474Z"
          fill={currentColor}
          stroke={currentColor}
        />
      </g>
      <defs>
        <clipPath id="clip0_1943_140299">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
