export const ModifierGridIcon = ({ bool }: { bool?: boolean }) => {
  const currentColor = bool ? '#2D3484' : '#20363F';

  return (
    <svg
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill={currentColor}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.97917 15.9437H5.97917C2.35917 15.9437 0.8125 14.397 0.8125 10.777V6.77702C0.8125 3.15702 2.35917 1.61035 5.97917 1.61035H9.97917C13.5992 1.61035 15.1458 3.15702 15.1458 6.77702V10.777C15.1458 14.397 13.6058 15.9437 9.97917 15.9437ZM5.97917 2.61035C2.90583 2.61035 1.8125 3.70368 1.8125 6.77702V10.777C1.8125 13.8504 2.90583 14.9437 5.97917 14.9437H9.97917C13.0525 14.9437 14.1458 13.8504 14.1458 10.777V6.77702C14.1458 3.70368 13.0525 2.61035 9.97917 2.61035H5.97917Z"
        fill={currentColor}
        className="cell-icons"
      />
      <path
        d="M2.17997 14.4103C2.05331 14.4103 1.92664 14.3636 1.82664 14.2636C1.63331 14.0703 1.63331 13.7503 1.82664 13.557L12.7933 2.59031C12.9866 2.39698 13.3066 2.39698 13.5 2.59031C13.6933 2.78365 13.6933 3.10365 13.5 3.29698L2.53331 14.2636C2.43997 14.3636 2.31331 14.4103 2.17997 14.4103Z"
        fill={currentColor}
        className="cell-icons"
      />
      <path
        d="M10.707 13.2777C10.4337 13.2777 10.207 13.051 10.207 12.7777V9.44434C10.207 9.171 10.4337 8.94434 10.707 8.94434C10.9804 8.94434 11.207 9.171 11.207 9.44434V12.7777C11.207 13.051 10.9804 13.2777 10.707 13.2777Z"
        fill={currentColor}
        className="cell-icons"
      />
      <path
        d="M12.3333 11.6104H9C8.72667 11.6104 8.5 11.3837 8.5 11.1104C8.5 10.837 8.72667 10.6104 9 10.6104H12.3333C12.6067 10.6104 12.8333 10.837 12.8333 11.1104C12.8333 11.3837 12.6067 11.6104 12.3333 11.6104Z"
        fill={currentColor}
        className="cell-icons"
      />
      <path
        d="M6.99935 6.27734H3.66602C3.39268 6.27734 3.16602 6.05068 3.16602 5.77734C3.16602 5.50401 3.39268 5.27734 3.66602 5.27734H6.99935C7.27268 5.27734 7.49935 5.50401 7.49935 5.77734C7.49935 6.05068 7.27268 6.27734 6.99935 6.27734Z"
        fill={currentColor}
        className="cell-icons"
      />
    </svg>
  );
};
