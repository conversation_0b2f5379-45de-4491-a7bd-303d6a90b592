export const EmailCredentialIcon = ({ bool }: { bool: boolean }) => {
  const currentColor = bool ? '#2D3484' : '#20363F';
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.75 15.4375H5.25C3.97616 15.4375 3.03925 15.0728 2.42074 14.4543C1.80223 13.8357 1.4375 12.8988 1.4375 11.625V6.375C1.4375 5.10116 1.80223 4.16425 2.42074 3.54574C3.03925 2.92723 3.97616 2.5625 5.25 2.5625H12.75C14.0238 2.5625 14.9607 2.92723 15.5793 3.54574C16.1978 4.16425 16.5625 5.10116 16.5625 6.375V11.625C16.5625 12.8988 16.1978 13.8357 15.5793 14.4543C14.9607 15.0728 14.0238 15.4375 12.75 15.4375ZM5.25 2.6875C4.10401 2.6875 3.15568 2.96596 2.49832 3.62332C1.84096 4.28068 1.5625 5.22901 1.5625 6.375V11.625C1.5625 12.771 1.84096 13.7193 2.49832 14.3767C3.15568 15.034 4.10401 15.3125 5.25 15.3125H12.75C13.896 15.3125 14.8443 15.034 15.5017 14.3767C16.159 13.7193 16.4375 12.771 16.4375 11.625V6.375C16.4375 5.22901 16.159 4.28068 15.5017 3.62332C14.8443 2.96596 13.896 2.6875 12.75 2.6875H5.25Z"
        stroke={currentColor}
      />
      <path
        d="M7.56105 8.67247L7.56107 8.67245L7.55712 8.6693L5.212 6.7962C5.19633 6.7831 5.18796 6.76603 5.18598 6.74931C5.18409 6.73334 5.18815 6.72199 5.19564 6.71277C5.20883 6.69654 5.22622 6.6879 5.24325 6.68588C5.25922 6.68399 5.27057 6.68805 5.27979 6.69554L5.27978 6.69555L5.28304 6.69816L7.6296 8.57241C7.62976 8.57254 7.62992 8.57267 7.63009 8.5728C8.01969 8.88529 8.52057 9.02561 8.99632 9.02561C9.47208 9.02561 9.97296 8.88529 10.3626 8.5728C10.3627 8.57267 10.3629 8.57254 10.363 8.57241L12.7096 6.69816L12.7096 6.69817L12.7129 6.69554C12.7241 6.68638 12.7392 6.68164 12.7558 6.68348C12.7716 6.68524 12.7808 6.69183 12.7865 6.69941L12.7917 6.70618L12.797 6.71277C12.8062 6.72405 12.8109 6.7391 12.8091 6.7557C12.8073 6.77153 12.8007 6.78069 12.7932 6.78643L12.7931 6.7863L12.783 6.7943L10.4355 8.6693L10.4355 8.66924L10.4287 8.67488C10.0525 8.98641 9.53342 9.15247 9.00008 9.15247C8.46762 9.15247 7.94658 8.98682 7.56105 8.67247Z"
        fill="#20363F"
        stroke={currentColor}
      />
    </svg>
  );
};
