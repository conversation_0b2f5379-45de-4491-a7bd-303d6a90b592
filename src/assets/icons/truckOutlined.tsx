export const TruckOutlinedIcon = ({ bool }: { bool?: boolean }) => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.8334 12.2917H1.66669C1.32502 12.2917 1.04169 12.0084 1.04169 11.6667V5.00002C1.04169 2.81669 2.81669 1.04169 5.00002 1.04169H12.5C12.8417 1.04169 13.125 1.32502 13.125 1.66669V10C13.125 11.2667 12.1 12.2917 10.8334 12.2917ZM2.29169 11.0417H10.8334C11.4084 11.0417 11.875 10.575 11.875 10V2.29169H5.00002C3.50835 2.29169 2.29169 3.50835 2.29169 5.00002V11.0417Z"
      fill={bool ? '#2D3484' : '#20363F'}
    />
    <path
      d="M15.8334 17.2917H15C14.6584 17.2917 14.375 17.0084 14.375 16.6667C14.375 16.0917 13.9084 15.625 13.3334 15.625C12.7584 15.625 12.2917 16.0917 12.2917 16.6667C12.2917 17.0084 12.0084 17.2917 11.6667 17.2917H8.33335C7.99169 17.2917 7.70835 17.0084 7.70835 16.6667C7.70835 16.0917 7.24169 15.625 6.66669 15.625C6.09169 15.625 5.62502 16.0917 5.62502 16.6667C5.62502 17.0084 5.34169 17.2917 5.00002 17.2917H4.16669C2.44169 17.2917 1.04169 15.8917 1.04169 14.1667V11.6667C1.04169 11.325 1.32502 11.0417 1.66669 11.0417H10.8334C11.4084 11.0417 11.875 10.575 11.875 10V4.16669C11.875 3.82502 12.1584 3.54169 12.5 3.54169H14.0334C14.8584 3.54169 15.6167 3.98337 16.025 4.70003L17.45 7.19169C17.5584 7.38336 17.5584 7.62502 17.45 7.81669C17.3417 8.00836 17.1334 8.12502 16.9084 8.12502H15.8334C15.7167 8.12502 15.625 8.21669 15.625 8.33335V10.8334C15.625 10.95 15.7167 11.0417 15.8334 11.0417H18.3334C18.675 11.0417 18.9584 11.325 18.9584 11.6667V14.1667C18.9584 15.8917 17.5584 17.2917 15.8334 17.2917ZM15.5417 16.0417H15.8334C16.8667 16.0417 17.7084 15.2 17.7084 14.1667V12.2917H15.8334C15.0334 12.2917 14.375 11.6334 14.375 10.8334V8.33335C14.375 7.53335 15.025 6.87502 15.8334 6.87502L14.9417 5.31669C14.7583 4.99169 14.4084 4.79169 14.0334 4.79169H13.125V10C13.125 11.2667 12.1 12.2917 10.8334 12.2917H2.29169V14.1667C2.29169 15.2 3.13335 16.0417 4.16669 16.0417H4.45836C4.73336 15.0834 5.61669 14.375 6.66669 14.375C7.71669 14.375 8.60002 15.0834 8.87502 16.0417H11.1333C11.4083 15.0834 12.2917 14.375 13.3417 14.375C14.3917 14.375 15.2667 15.0834 15.5417 16.0417Z"
      fill={bool ? '#2D3484' : '#20363F'}
    />
    <path
      d="M6.66667 18.9583C5.4 18.9583 4.375 17.9333 4.375 16.6667C4.375 15.4 5.4 14.375 6.66667 14.375C7.93333 14.375 8.95833 15.4 8.95833 16.6667C8.95833 17.9333 7.93333 18.9583 6.66667 18.9583ZM6.66667 15.625C6.09167 15.625 5.625 16.0917 5.625 16.6667C5.625 17.2417 6.09167 17.7083 6.66667 17.7083C7.24167 17.7083 7.70833 17.2417 7.70833 16.6667C7.70833 16.0917 7.24167 15.625 6.66667 15.625Z"
      fill={bool ? '#2D3484' : '#20363F'}
    />
    <path
      d="M13.3334 18.9583C12.0667 18.9583 11.0417 17.9333 11.0417 16.6667C11.0417 15.4 12.0667 14.375 13.3334 14.375C14.6 14.375 15.625 15.4 15.625 16.6667C15.625 17.9333 14.6 18.9583 13.3334 18.9583ZM13.3334 15.625C12.7584 15.625 12.2917 16.0917 12.2917 16.6667C12.2917 17.2417 12.7584 17.7083 13.3334 17.7083C13.9084 17.7083 14.375 17.2417 14.375 16.6667C14.375 16.0917 13.9084 15.625 13.3334 15.625Z"
      fill={bool ? '#2D3484' : '#20363F'}
    />
    <path
      d="M18.3333 12.2917H15.8333C15.0333 12.2917 14.375 11.6333 14.375 10.8333V8.33333C14.375 7.53333 15.0333 6.875 15.8333 6.875H16.9083C17.1333 6.875 17.3417 6.99167 17.45 7.19167L18.875 9.69167C18.925 9.78334 18.9583 9.89167 18.9583 10V11.6667C18.9583 12.0083 18.675 12.2917 18.3333 12.2917ZM15.8333 8.125C15.7167 8.125 15.625 8.21667 15.625 8.33333V10.8333C15.625 10.95 15.7167 11.0417 15.8333 11.0417H17.7083V10.1667L16.5417 8.125H15.8333Z"
      fill={bool ? '#2D3484' : '#20363F'}
    />
  </svg>
);
