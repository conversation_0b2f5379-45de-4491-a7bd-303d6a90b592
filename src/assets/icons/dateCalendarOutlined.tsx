import { PRIMARY } from "@/styles/colorConstants";

export const DateCalendarIcon = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.66602 4.7915C6.32435 4.7915 6.04102 4.50817 6.04102 4.1665V1.6665C6.04102 1.32484 6.32435 1.0415 6.66602 1.0415C7.00768 1.0415 7.29102 1.32484 7.29102 1.6665V4.1665C7.29102 4.50817 7.00768 4.7915 6.66602 4.7915Z"
        fill={PRIMARY[200]}
      />
      <path
        d="M13.334 4.7915C12.9923 4.7915 12.709 4.50817 12.709 4.1665V1.6665C12.709 1.32484 12.9923 1.0415 13.334 1.0415C13.6757 1.0415 13.959 1.32484 13.959 1.6665V4.1665C13.959 4.50817 13.6757 4.7915 13.334 4.7915Z"
        fill={PRIMARY[200]}
      />
      <path
        d="M7.08333 12.0834C6.975 12.0834 6.86667 12.0584 6.76667 12.0168C6.65833 11.9751 6.575 11.9168 6.49167 11.8418C6.34167 11.6834 6.25 11.4751 6.25 11.2501C6.25 11.1418 6.275 11.0334 6.31667 10.9334C6.35833 10.8334 6.41667 10.7418 6.49167 10.6584C6.575 10.5834 6.65833 10.5251 6.76667 10.4834C7.06667 10.3584 7.44167 10.4251 7.675 10.6584C7.825 10.8168 7.91667 11.0334 7.91667 11.2501C7.91667 11.3001 7.90833 11.3584 7.9 11.4168C7.89167 11.4668 7.875 11.5168 7.85 11.5668C7.83333 11.6168 7.80833 11.6668 7.775 11.7168C7.75 11.7584 7.70833 11.8001 7.675 11.8418C7.51667 11.9918 7.3 12.0834 7.08333 12.0834Z"
        fill={PRIMARY[200]}
      />
      <path
        d="M9.99935 12.0832C9.89102 12.0832 9.78268 12.0582 9.68268 12.0166C9.57435 11.9749 9.49102 11.9166 9.40768 11.8416C9.25768 11.6832 9.16602 11.4749 9.16602 11.2499C9.16602 11.1416 9.19102 11.0332 9.23268 10.9332C9.27435 10.8332 9.33268 10.7416 9.40768 10.6582C9.49102 10.5832 9.57435 10.5249 9.68268 10.4832C9.98268 10.3499 10.3577 10.4249 10.591 10.6582C10.741 10.8166 10.8327 11.0332 10.8327 11.2499C10.8327 11.2999 10.8243 11.3582 10.816 11.4166C10.8077 11.4666 10.791 11.5166 10.766 11.5666C10.7493 11.6166 10.7243 11.6666 10.691 11.7166C10.666 11.7582 10.6243 11.7999 10.591 11.8416C10.4327 11.9916 10.216 12.0832 9.99935 12.0832Z"
        fill={PRIMARY[200]}
      />
      <path
        d="M12.9173 12.0832C12.809 12.0832 12.7007 12.0582 12.6007 12.0166C12.4923 11.9749 12.409 11.9166 12.3257 11.8416C12.2923 11.7999 12.259 11.7582 12.2256 11.7166C12.1923 11.6666 12.1673 11.6166 12.1507 11.5666C12.1257 11.5166 12.109 11.4666 12.1007 11.4166C12.0923 11.3582 12.084 11.2999 12.084 11.2499C12.084 11.0332 12.1757 10.8166 12.3257 10.6582C12.409 10.5832 12.4923 10.5249 12.6007 10.4832C12.909 10.3499 13.2757 10.4249 13.509 10.6582C13.659 10.8166 13.7507 11.0332 13.7507 11.2499C13.7507 11.2999 13.7423 11.3582 13.734 11.4166C13.7257 11.4666 13.709 11.5166 13.684 11.5666C13.6673 11.6166 13.6423 11.6666 13.609 11.7166C13.584 11.7582 13.5423 11.7999 13.509 11.8416C13.3507 11.9916 13.134 12.0832 12.9173 12.0832Z"
        fill={PRIMARY[200]}
      />
      <path
        d="M7.08333 15C6.975 15 6.86667 14.975 6.76667 14.9333C6.66667 14.8917 6.575 14.8333 6.49167 14.7583C6.34167 14.6 6.25 14.3833 6.25 14.1667C6.25 14.0583 6.275 13.95 6.31667 13.85C6.35833 13.7417 6.41667 13.65 6.49167 13.575C6.8 13.2667 7.36667 13.2667 7.675 13.575C7.825 13.7333 7.91667 13.95 7.91667 14.1667C7.91667 14.3833 7.825 14.6 7.675 14.7583C7.51667 14.9083 7.3 15 7.08333 15Z"
        fill={PRIMARY[200]}
      />
      <path
        d="M9.99935 15C9.78268 15 9.56602 14.9083 9.40768 14.7583C9.25768 14.6 9.16602 14.3833 9.16602 14.1667C9.16602 14.0583 9.19102 13.95 9.23268 13.85C9.27435 13.7417 9.33268 13.65 9.40768 13.575C9.71602 13.2667 10.2827 13.2667 10.591 13.575C10.666 13.65 10.7243 13.7417 10.766 13.85C10.8077 13.95 10.8327 14.0583 10.8327 14.1667C10.8327 14.3833 10.741 14.6 10.591 14.7583C10.4327 14.9083 10.216 15 9.99935 15Z"
        fill={PRIMARY[200]}
      />
      <path
        d="M12.9173 14.9999C12.7007 14.9999 12.484 14.9083 12.3257 14.7583C12.2507 14.6833 12.1923 14.5916 12.1507 14.4833C12.109 14.3833 12.084 14.2749 12.084 14.1666C12.084 14.0583 12.109 13.9499 12.1507 13.8499C12.1923 13.7416 12.2507 13.6499 12.3257 13.5749C12.5173 13.3833 12.809 13.2916 13.0757 13.3499C13.134 13.3583 13.184 13.3749 13.234 13.3999C13.284 13.4166 13.334 13.4416 13.384 13.4749C13.4257 13.4999 13.4673 13.5416 13.509 13.5749C13.659 13.7333 13.7507 13.9499 13.7507 14.1666C13.7507 14.3833 13.659 14.5999 13.509 14.7583C13.3507 14.9083 13.134 14.9999 12.9173 14.9999Z"
        fill={PRIMARY[200]}
      />
      <path
        d="M17.0827 8.20019H2.91602C2.57435 8.20019 2.29102 7.91686 2.29102 7.5752C2.29102 7.23353 2.57435 6.9502 2.91602 6.9502H17.0827C17.4243 6.9502 17.7077 7.23353 17.7077 7.5752C17.7077 7.91686 17.4243 8.20019 17.0827 8.20019Z"
        fill={PRIMARY[200]}
      />
      <path
        d="M13.3333 18.9582H6.66667C3.625 18.9582 1.875 17.2082 1.875 14.1665V7.08317C1.875 4.0415 3.625 2.2915 6.66667 2.2915H13.3333C16.375 2.2915 18.125 4.0415 18.125 7.08317V14.1665C18.125 17.2082 16.375 18.9582 13.3333 18.9582ZM6.66667 3.5415C4.28333 3.5415 3.125 4.69984 3.125 7.08317V14.1665C3.125 16.5498 4.28333 17.7082 6.66667 17.7082H13.3333C15.7167 17.7082 16.875 16.5498 16.875 14.1665V7.08317C16.875 4.69984 15.7167 3.5415 13.3333 3.5415H6.66667Z"
        fill={PRIMARY[200]}
      />
    </svg>
  );
};
