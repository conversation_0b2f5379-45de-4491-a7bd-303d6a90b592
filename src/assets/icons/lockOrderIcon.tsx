const LockOrderIcon = () => {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 8.12972C11.7267 8.12972 11.5 7.90305 11.5 7.62972V6.29639C11.5 4.19639 10.9067 2.79639 8 2.79639C5.09333 2.79639 4.5 4.19639 4.5 6.29639V7.62972C4.5 7.90305 4.27333 8.12972 4 8.12972C3.72667 8.12972 3.5 7.90305 3.5 7.62972V6.29639C3.5 4.36305 3.96667 1.79639 8 1.79639C12.0333 1.79639 12.5 4.36305 12.5 6.29639V7.62972C12.5 7.90305 12.2733 8.12972 12 8.12972Z"
        fill="#090A1A"
        className="cell-icons"
      />
      <path
        d="M8.00065 13.7965C6.80732 13.7965 5.83398 12.8231 5.83398 11.6298C5.83398 10.4365 6.80732 9.46313 8.00065 9.46313C9.19398 9.46313 10.1673 10.4365 10.1673 11.6298C10.1673 12.8231 9.19398 13.7965 8.00065 13.7965ZM8.00065 10.4631C7.36065 10.4631 6.83398 10.9898 6.83398 11.6298C6.83398 12.2698 7.36065 12.7965 8.00065 12.7965C8.64065 12.7965 9.16732 12.2698 9.16732 11.6298C9.16732 10.9898 8.64065 10.4631 8.00065 10.4631Z"
        fill="#090A1A"
        className="cell-icons"
      />
      <path
        d="M11.334 16.1299H4.66732C1.72732 16.1299 0.833984 15.2365 0.833984 12.2965V10.9632C0.833984 8.02322 1.72732 7.12988 4.66732 7.12988H11.334C14.274 7.12988 15.1673 8.02322 15.1673 10.9632V12.2965C15.1673 15.2365 14.274 16.1299 11.334 16.1299ZM4.66732 8.12988C2.28065 8.12988 1.83398 8.58322 1.83398 10.9632V12.2965C1.83398 14.6765 2.28065 15.1299 4.66732 15.1299H11.334C13.7207 15.1299 14.1673 14.6765 14.1673 12.2965V10.9632C14.1673 8.58322 13.7207 8.12988 11.334 8.12988H4.66732Z"
        fill="#090A1A"
        className="cell-icons"
      />
    </svg>
  );
};

export default LockOrderIcon;
