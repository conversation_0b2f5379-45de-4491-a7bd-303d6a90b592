import { PRIMARY } from '@/styles/colorConstants';
import { ThemeConfig } from 'antd';

export const ANTD_THEME: ThemeConfig = {
  token: {
    fontFamily: 'Inter',
  },
  components: {
    Input: {
      activeBorderColor: PRIMARY[600],
      hoverBorderColor: PRIMARY[600],
      colorBorder: PRIMARY[100],
    },

    Button: {
      colorPrimaryBorderHover: PRIMARY[600],
      colorPrimaryBgHover: PRIMARY[600],
      colorPrimary: PRIMARY[600],
      colorPrimaryHover: PRIMARY[500],
      colorPrimaryActive: PRIMARY[600],
      colorPrimaryBg: PRIMARY[600],
      borderRadius: 8,
    },
    Select: {
      activeBorderColor: PRIMARY[600],
      hoverBorderColor: PRIMARY[600],
      optionSelectedColor: PRIMARY[600],
      optionSelectedFontWeight: 400,
      borderRadius: 8,
    },
    Tabs: {
      itemHoverColor: PRIMARY[600],
    },
    DatePicker: {
      colorPrimary: PRIMARY[600],
      hoverBorderColor: PRIMARY[600],
    },
    Checkbox: {
      colorPrimary: PRIMARY[600],
    },
    Radio: {
      colorPrimary: PRIMARY[600],
      buttonSolidCheckedBg: PRIMARY[600],
      buttonSolidCheckedHoverBg: PRIMARY[500],
    },
  },
};
