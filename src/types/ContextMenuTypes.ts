import { CellContextMenuEvent } from 'ag-grid-community';

export interface IContextMenuConfig {
  minimumSubMenuSize: number;
}

/**
 * Interface for the props used by the GridContextMenu component.
 *
 * @param {IContextMenuItems[]} menuItems - An array of menu items, each potentially containing
 * submenus or other custom properties to define the context menu structure.
 */

export interface IGridContextMenuProps {
  menuItems: IContextMenuItems[];
  contextMenuEvent: CellContextMenuEvent | undefined;
}

/**
 * Interface for the props used by the menu click handler.
 *
 * @param {IContextMenuItems} item - clicked item of context menu
 * @param closeContextMenu - close context menu handler function
 */

export interface onContextMenuItemClickParams<T = any> {
  item: IContextMenuItems;
  closeContextMenu: () => void;
  rowData: T;
}
export interface IContextMenuItems {
  label: string | JSX.Element;
  icon?: React.ElementType | string;
  subMenu?: IContextMenuItems[];
  key: string;
  onClick?: (params: onContextMenuItemClickParams) => void | Promise<void>;
  disabled?: (params: CellContextMenuEvent) => boolean;
}
