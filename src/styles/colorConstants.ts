/**
 * Color constants for use in component files
 * These values reference the CSS variables defined in theme.css
 */

// Gray colors
export const GRAY = {
  25: 'var(--grey-25)', // AA 7.49
  50: 'var(--grey-50)', // AA 7.35
  100: 'var(--grey-100)', // AA 6.97
  200: 'var(--grey-200)', // AA 7.68
  300: 'var(--grey-300)', // 148
  400: 'var(--grey-400)', // 2.58
  500: 'var(--grey-500)', // AA 4.95
  600: 'var(--grey-600)', // AAA
  800: 'var(--grey-800)', // AAA
};

// Brand/Primary colors
export const PRIMARY = {
  25: 'var(--primary-25)', // AA 6.39
  50: 'var(--primary-50)', // AA 6.16
  100: 'var(--primary-100)', // AA 5.74
  200: 'var(--primary-200)', // AA 4.83
  300: 'var(--primary-300)', // 1.70
  400: 'var(--primary-400)', // 2.49
  500: 'var(--primary-500)', // 3.33
  600: 'var(--primary-600)', // AA 4.96
  800: 'var(--primary-800)', // AAA
  900: 'var(--primary-900)', // AAA
};
export const SECONDARY = {
  25: 'var(--secondary-25)', // AA 6.39
  50: 'var(--secondary-50)', // AA 6.16
  100: 'var(--secondary-100)', // AA 5.74
  200: 'var(--secondary-200)', // AA 4.83
  300: 'var(--secondary-300)', // 1.70
  400: 'var(--secondary-400)', // 2.49
  500: 'var(--secondary-500)', // 3.33
  600: 'var(--secondary-600)', // AA 4.96
  800: 'var(--secondary-800)', // AAA
};

// Error colors
export const ERROR = {
  25: 'var(--error-25)', // AA 6.4
  50: 'var(--error-50)', // AA 6.06
  100: 'var(--error-100)', // AA 5.4
  200: 'var(--error-200)', // AA 4.56
  300: 'var(--error-300)', // 1.95
  400: 'var(--error-400)', // 2.78
  500: 'var(--error-500)', // 3.76
  600: 'var(--error-600)', // AA 4.82
  800: 'var(--error-800)', // AAA
};

// Warning colors
export const WARNING = {
  25: 'var(--warning-25)', // AA 5.28
  50: 'var(--warning-50)', // AA 5.17
  100: 'var(--warning-100)', // AA 4.75
  200: 'var(--warning-200)', // 4.15
  300: 'var(--warning-300)', // 1.94
  400: 'var(--warning-400)', // 1.84
  500: 'var(--warning-500)', // 3.34
  600: 'var(--warning-600)', // 3.49
  800: 'var(--warning-800)', // AAA
};

// Success colors
export const SUCCESS = {
  25: 'var(--success-25)', // AA 5.31
  50: 'var(--success-50)', // AA 5.17
  100: 'var(--success-100)', // AA 4.79
  200: 'var(--success-200)', // 4.24
  300: 'var(--success-300)', // 1.52
  400: 'var(--success-400)', // 1.91
  500: 'var(--success-500)', // 2.83
  600: 'var(--success-600)', // 3.74
  800: 'var(--success-800)', // AAA
};

// Common colors
export const COMMON = {
  WHITE: 'var(--white)',
  BLACK_TEXT: 'var(--black-text)',
  TOOLTIP_BG: 'var(--tooltipBg)',
  INPUT_BORDER: 'var(--input-border)',
  BLACK_TRANSPARENT_04: 'rgba(0, 0, 0, 0.04)',
};

// Semantic color aliases for specific use cases
export const SEMANTIC = {
  // Text colors
  TEXT_PRIMARY: GRAY[800],
  TEXT_SECONDARY: GRAY[600],
  TEXT_TERTIARY: GRAY[500],
  TEXT_DISABLED: GRAY[400],

  // Background colors
  BACKGROUND_DEFAULT: COMMON.WHITE,
  BACKGROUND_LIGHT: GRAY[50],
  BACKGROUND_LIGHTER: GRAY[25],

  // Border colors
  BORDER_DEFAULT: GRAY[300],
  BORDER_LIGHT: GRAY[200],

  // Action colors
  ACTION_PRIMARY: PRIMARY[600],
  ACTION_PRIMARY_HOVER: PRIMARY[500],
  ACTION_PRIMARY_ACTIVE: PRIMARY[800],
  ACTION_DANGER: ERROR[600],
  ACTION_DANGER_HOVER: ERROR[500],
  ACTION_SUCCESS: SUCCESS[600],
  ACTION_WARNING: WARNING[600],
};

// Export all color categories
export const COLORS = {
  GRAY,
  PRIMARY,
  ERROR,
  WARNING,
  SUCCESS,
  COMMON,
  SEMANTIC,
};

export default COLORS;
