import { QueryHookKey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { OrdersService } from './orders.service';
import { CreateOrdersDto, IOrderPaginatedResponse } from './order.types';

export const ordersService = new OrdersService(apiClient.getAxiosInstance());

export const orderServiceHook = createEntityHooks<
  IOrderPaginatedResponse,
  CreateOrdersDto,
  CreateOrdersDto
>(QueryHookKey.Orders, ordersService);
