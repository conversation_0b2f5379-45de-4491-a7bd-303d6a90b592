import { CustomerCategories } from './customerCategories.types';

export interface CreateCustomerDto {
  companyName: string;
  contactName: string;
  accountNumber: string;
  email: string;
  phoneNumberCountryCode: string;
  phoneNumber: string;
  phoneExtension: string;
  faxCountryCode: string;
  faxNumber: string;
  website: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
  status: string;
  categories: CustomerCategories[];
}

export interface ICustomer extends CreateCustomerDto {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface CustomerPaginatedResponse {
  data: ICustomer[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface AllCustomerListResponse {
  companyName: string;
  contactName: string;
  id: string;
}
