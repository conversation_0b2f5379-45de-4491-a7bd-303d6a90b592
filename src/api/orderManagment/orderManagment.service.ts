import { apiClient } from '..';

const api = apiClient.getAxiosInstance();

export const assignOrderToDriver = async ({
  orderId,
  driverId,
}: {
  orderId: string;
  driverId: string;
}) => {
  const response = await api.post(
    `/api/v1/admin/order-management/orders/${orderId}/assign/${driverId}`
  );
  return response.data;
};

export const unassignOrderToDriver = async (orderId: string) => {
  const response = await api.post(`/api/v1/admin/order-management/orders/${orderId}/unassign`);
  return response.data;
};
