import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { QueryHookKey } from '@/constant/QueryHookConstant';
import { queryClient } from '../core/react-query-hooks';
import { assignOrderToDriver, unassignOrderToDriver } from './orderManagment.service';
import notificationManagerInstance from '@/hooks/useNotificationManger';
import { translator } from '@/i18n/languageLoader';

// Types for the assign order mutation
interface AssignOrderToDriverParams {
  orderId: string;
  driverId: string;
}

// Generic response type - update this based on your actual API response structure
interface AssignOrderToDriverResponse {
  [key: string]: any;
}

// Generic response type for unassign - update this based on your actual API response structure
interface UnassignOrderToDriverResponse {
  [key: string]: any;
}

/**
 * Custom hook for assigning an order to a driver
 * @param options - Optional mutation options
 * @returns useMutation hook for assigning orders to drivers
 */
export const useAssignOrderToDriver = (
  options?: Omit<
    UseMutationOptions<AssignOrderToDriverResponse, AxiosError, AssignOrderToDriverParams>,
    'mutationFn'
  >
) => {
  return useMutation({
    mutationFn: ({ orderId, driverId }: AssignOrderToDriverParams) =>
      assignOrderToDriver({ orderId, driverId }),
    onSuccess: (data, variables, context) => {
      // Show success notification
      notificationManagerInstance.success({
        message: translator('common.success'),
        description: 'Order assigned to driver successfully',
      });

      // Invalidate relevant queries when assignment is successful
      queryClient.invalidateQueries({ queryKey: [QueryHookKey.Orders] });
      queryClient.invalidateQueries({ queryKey: [QueryHookKey.Order_Management] });

      // Call the custom onSuccess if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
    onError: (error, variables, context) => {
      // Show error notification
      notificationManagerInstance.error({
        message: translator('common.error'),
        description: 'Failed to assign order to driver',
      });

      // Call the custom onError if provided
      if (options?.onError) {
        options.onError(error, variables, context);
      }
    },
    ...options,
  });
};

/**
 * Custom hook for unassigning an order from a driver
 * @param options - Optional mutation options
 * @returns useMutation hook for unassigning orders from drivers
 */
export const useUnassignOrderToDriver = (
  options?: Omit<
    UseMutationOptions<UnassignOrderToDriverResponse, AxiosError, string>,
    'mutationFn'
  >
) => {
  return useMutation({
    mutationFn: (orderId: string) => unassignOrderToDriver(orderId),
    onSuccess: (data, variables, context) => {
      // Show success notification
      notificationManagerInstance.success({
        message: translator('common.success'),
        description: 'Order unassigned from driver successfully',
      });

      // Invalidate relevant queries when unassignment is successful
      queryClient.invalidateQueries({ queryKey: [QueryHookKey.Orders] });
      queryClient.invalidateQueries({ queryKey: [QueryHookKey.Order_Management] });

      // Call the custom onSuccess if provided
      if (options?.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
    onError: (error, variables, context) => {
      // Show error notification
      notificationManagerInstance.error({
        message: translator('common.error'),
        description: 'Failed to unassign order from driver',
      });

      // Call the custom onError if provided
      if (options?.onError) {
        options.onError(error, variables, context);
      }
    },
    ...options,
  });
};

// Export the hook as part of an object for consistency with other hook files
export const orderManagementHooks = {
  useAssignOrderToDriver,
  useUnassignOrderToDriver,
};
